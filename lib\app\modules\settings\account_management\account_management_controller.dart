import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/storage_service.dart';
import '../../../routes/app_routes.dart';

class AccountManagementController extends GetxController {
  final RxBool twoFactorEnabled = false.obs;
  final RxBool loginNotifications = true.obs;
  final RxBool dataSync = true.obs;
  final RxBool autoLogout = false.obs;
  final RxInt autoLogoutMinutes = 30.obs;
  final RxString lastLoginDate = ''.obs;
  final RxString accountCreatedDate = ''.obs;
  final RxInt loginAttempts = 0.obs;
  final RxBool accountLocked = false.obs;

  final List<int> autoLogoutOptions = [15, 30, 60, 120, 240]; // minutes

  @override
  void onInit() {
    super.onInit();
    _loadAccountSettings();
  }

  void _loadAccountSettings() {
    final storage = StorageService.to;
    twoFactorEnabled.value = storage.getBool('two_factor_enabled') ?? false;
    loginNotifications.value = storage.getBool('login_notifications') ?? true;
    dataSync.value = storage.getBool('data_sync') ?? true;
    autoLogout.value = storage.getBool('auto_logout') ?? false;
    autoLogoutMinutes.value = storage.getInt('auto_logout_minutes') ?? 30;
    lastLoginDate.value = storage.getString('last_login_date').isEmpty 
        ? 'Never' 
        : storage.getString('last_login_date');
    accountCreatedDate.value = storage.getString('account_created_date').isEmpty 
        ? 'Unknown' 
        : storage.getString('account_created_date');
    loginAttempts.value = storage.getInt('login_attempts') ?? 0;
    accountLocked.value = storage.getBool('account_locked') ?? false;
  }

  void toggleTwoFactor() {
    if (twoFactorEnabled.value) {
      _disableTwoFactor();
    } else {
      _enableTwoFactor();
    }
  }

  void _enableTwoFactor() {
    Get.dialog(
      AlertDialog(
        title: const Text('Enable Two-Factor Authentication'),
        content: const Text(
          'Two-factor authentication adds an extra layer of security to your account. You will need to verify your identity using a second method when logging in.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _setupTwoFactor();
            },
            child: const Text('Enable'),
          ),
        ],
      ),
    );
  }

  void _setupTwoFactor() {
    // Simulate 2FA setup process
    Get.dialog(
      AlertDialog(
        title: const Text('Setup Two-Factor Authentication'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Scan this QR code with your authenticator app:'),
            const SizedBox(height: 16),
            Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text('QR Code\n(Simulated)', textAlign: TextAlign.center),
              ),
            ),
            const SizedBox(height: 16),
            const Text('Or enter this code manually: ABCD-EFGH-IJKL'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              twoFactorEnabled.value = true;
              StorageService.to.setBool('two_factor_enabled', true);
              Get.snackbar(
                'Two-Factor Authentication',
                'Two-factor authentication has been enabled',
                snackPosition: SnackPosition.BOTTOM,
                duration: const Duration(seconds: 3),
              );
            },
            child: const Text('Complete Setup'),
          ),
        ],
      ),
    );
  }

  void _disableTwoFactor() {
    Get.dialog(
      AlertDialog(
        title: const Text('Disable Two-Factor Authentication'),
        content: const Text(
          'Are you sure you want to disable two-factor authentication? This will make your account less secure.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              twoFactorEnabled.value = false;
              StorageService.to.setBool('two_factor_enabled', false);
              Get.snackbar(
                'Two-Factor Authentication',
                'Two-factor authentication has been disabled',
                snackPosition: SnackPosition.BOTTOM,
                duration: const Duration(seconds: 2),
              );
            },
            child: const Text('Disable', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void toggleLoginNotifications() {
    loginNotifications.value = !loginNotifications.value;
    StorageService.to.setBool('login_notifications', loginNotifications.value);
    Get.snackbar(
      'Login Notifications',
      loginNotifications.value 
          ? 'You will be notified of login attempts'
          : 'Login notifications disabled',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void toggleDataSync() {
    dataSync.value = !dataSync.value;
    StorageService.to.setBool('data_sync', dataSync.value);
    Get.snackbar(
      'Data Synchronization',
      dataSync.value 
          ? 'Data sync enabled across devices'
          : 'Data sync disabled',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void toggleAutoLogout() {
    autoLogout.value = !autoLogout.value;
    StorageService.to.setBool('auto_logout', autoLogout.value);
    Get.snackbar(
      'Auto Logout',
      autoLogout.value 
          ? 'Auto logout enabled after ${autoLogoutMinutes.value} minutes'
          : 'Auto logout disabled',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void changeAutoLogoutTime() {
    Get.dialog(
      AlertDialog(
        title: const Text('Auto Logout Time'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: autoLogoutOptions.length,
            itemBuilder: (context, index) {
              final minutes = autoLogoutOptions[index];
              return Obx(() => RadioListTile<int>(
                    title: Text('${minutes} minutes'),
                    value: minutes,
                    groupValue: autoLogoutMinutes.value,
                    onChanged: (value) {
                      if (value != null) {
                        autoLogoutMinutes.value = value;
                        StorageService.to.setInt('auto_logout_minutes', value);
                        Get.back();
                        Get.snackbar(
                          'Auto Logout Time',
                          'Auto logout set to $value minutes',
                          snackPosition: SnackPosition.BOTTOM,
                          duration: const Duration(seconds: 2),
                        );
                      }
                    },
                    activeColor: Theme.of(Get.context!).colorScheme.primary,
                  ));
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void changePassword() {
    Get.toNamed(AppRoutes.changePassword);
  }

  void viewLoginHistory() {
    Get.dialog(
      AlertDialog(
        title: const Text('Login History'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView(
            children: [
              _buildLoginHistoryItem('Today, 2:30 PM', 'Mobile App', 'Current session'),
              _buildLoginHistoryItem('Yesterday, 9:15 AM', 'Web Browser', 'Chrome on Windows'),
              _buildLoginHistoryItem('2 days ago, 6:45 PM', 'Mobile App', 'Previous session'),
              _buildLoginHistoryItem('1 week ago, 11:20 AM', 'Web Browser', 'Safari on macOS'),
              _buildLoginHistoryItem('2 weeks ago, 4:10 PM', 'Mobile App', 'iOS'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginHistoryItem(String date, String device, String details) {
    return ListTile(
      leading: Icon(
        device.contains('Mobile') ? Icons.phone_android : Icons.computer,
        color: Colors.blue,
      ),
      title: Text(date),
      subtitle: Text('$device - $details'),
      dense: true,
    );
  }

  void exportAccountData() {
    Get.dialog(
      AlertDialog(
        title: const Text('Export Account Data'),
        content: const Text(
          'This will create a file containing all your account data. The export may take a few minutes to complete.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _simulateDataExport();
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _simulateDataExport() {
    Get.snackbar(
      'Exporting Data',
      'Preparing your account data for export...',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
      showProgressIndicator: true,
    );

    Future.delayed(const Duration(seconds: 4), () {
      Get.snackbar(
        'Export Complete',
        'Your account data has been exported successfully',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    });
  }

  void deleteAccount() {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Account'),
        content: const Text(
          'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _confirmAccountDeletion();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _confirmAccountDeletion() {
    Get.dialog(
      AlertDialog(
        title: const Text('Final Confirmation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Type "DELETE" to confirm account deletion:'),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                hintText: 'Type DELETE here',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                // Handle confirmation text
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                'Account Deletion',
                'Account deletion feature will be implemented soon',
                snackPosition: SnackPosition.BOTTOM,
                duration: const Duration(seconds: 3),
              );
            },
            child: const Text('Confirm Deletion', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void unlockAccount() {
    if (accountLocked.value) {
      accountLocked.value = false;
      loginAttempts.value = 0;
      StorageService.to.setBool('account_locked', false);
      StorageService.to.setInt('login_attempts', 0);
      Get.snackbar(
        'Account Unlocked',
        'Your account has been unlocked successfully',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    }
  }
}
