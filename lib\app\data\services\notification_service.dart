import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import '../models/notification_model.dart';
import '../../core/services/network_service.dart';

class NotificationService extends GetxService {
  static NotificationService get to => Get.find();

  final NetworkService _networkService = NetworkService.to;

  Future<List<NotificationItem>> getNotifications({
    int page = 1,
    int limit = 20,
    bool? isRead,
    NotificationType? type,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (isRead != null) queryParams['is_read'] = isRead;
      if (type != null) queryParams['type'] = type.name;

      final response = await _networkService.get(
        '/notifications',
        queryParameters: queryParams,
      );

      final List<dynamic> notificationList = response.data['notifications'];
      return notificationList.map((json) => NotificationItem.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<NotificationItem> getNotification(String notificationId) async {
    try {
      final response = await _networkService.get('/notifications/$notificationId');
      return NotificationItem.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      await _networkService.put('/notifications/$notificationId/read');
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> markAllAsRead() async {
    try {
      await _networkService.put('/notifications/mark-all-read');
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      await _networkService.delete('/notifications/$notificationId');
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> deleteAllRead() async {
    try {
      await _networkService.delete('/notifications/read');
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<int> getUnreadCount() async {
    try {
      final response = await _networkService.get('/notifications/unread-count');
      return response.data['count'] as int;
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> updateNotificationSettings({
    bool? pushEnabled,
    bool? emailEnabled,
    List<NotificationType>? enabledTypes,
    Map<String, bool>? typeSettings,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (pushEnabled != null) data['push_enabled'] = pushEnabled;
      if (emailEnabled != null) data['email_enabled'] = emailEnabled;
      if (enabledTypes != null) {
        data['enabled_types'] = enabledTypes.map((type) => type.name).toList();
      }
      if (typeSettings != null) data['type_settings'] = typeSettings;

      await _networkService.put('/notifications/settings', data: data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Map<String, dynamic>> getNotificationSettings() async {
    try {
      final response = await _networkService.get('/notifications/settings');
      return response.data;
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> registerDeviceToken({
    required String token,
    required String platform, // 'ios' or 'android'
  }) async {
    try {
      await _networkService.post('/notifications/device-token', data: {
        'token': token,
        'platform': platform,
      });
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> unregisterDeviceToken(String token) async {
    try {
      await _networkService.delete('/notifications/device-token', data: {
        'token': token,
      });
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> sendTestNotification() async {
    try {
      await _networkService.post('/notifications/test');
    } catch (e) {
      throw _handleError(e);
    }
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Connection timeout. Please check your internet connection.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'];
          
          if (statusCode == 404) {
            return 'Notification not found.';
          } else if (statusCode == 422) {
            return message ?? 'Validation error. Please check your input.';
          } else {
            return message ?? 'Server error. Please try again later.';
          }
        case DioExceptionType.cancel:
          return 'Request was cancelled.';
        case DioExceptionType.connectionError:
          return 'No internet connection. Please check your network.';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
    return error.toString();
  }
}
