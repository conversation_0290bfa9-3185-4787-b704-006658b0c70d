import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/utils/responsive_utils.dart';
import '../../core/services/accessibility_service.dart';

/// Responsive and accessible card widget
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? backgroundColor;
  final double? elevation;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final String? semanticLabel;
  final String? semanticHint;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.onTap,
    this.semanticLabel,
    this.semanticHint,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final responsivePadding = padding ?? ResponsiveUtils.responsivePadding(context);
    final responsiveMargin = margin ?? ResponsiveUtils.responsive(
      context,
      mobile: const EdgeInsets.all(8),
      tablet: const EdgeInsets.all(12),
      desktop: const EdgeInsets.all(16),
    );

    Widget card = Card(
      color: backgroundColor,
      elevation: elevation ?? 0,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      margin: responsiveMargin,
      child: Padding(
        padding: responsivePadding,
        child: child,
      ),
    );

    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        child: card,
      );
    }

    if (semanticLabel != null || semanticHint != null) {
      card = Semantics(
        label: semanticLabel,
        hint: semanticHint,
        button: onTap != null,
        child: card,
      );
    }

    return card;
  }
}

/// Responsive and accessible button
class ResponsiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final Widget? icon;
  final bool isLoading;
  final String? semanticLabel;
  final String? semanticHint;

  const ResponsiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isLoading = false,
    this.semanticLabel,
    this.semanticHint,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final accessibilityService = AccessibilityService.to;
    
    final buttonHeight = ResponsiveUtils.responsiveButtonHeight(context);
    final fontSize = ResponsiveUtils.responsiveFontSize(
      context,
      mobile: 16,
      tablet: 18,
      desktop: 20,
    );

    final buttonStyle = style ?? ElevatedButton.styleFrom(
      minimumSize: Size(double.infinity, buttonHeight),
      textStyle: accessibilityService.getAccessibleTextStyle(
        TextStyle(fontSize: fontSize),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );

    Widget button;
    if (icon != null) {
      button = ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading ? const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ) : icon!,
        label: Text(text),
        style: buttonStyle,
      );
    } else {
      button = ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Text(text),
      );
    }

    return Semantics(
      label: semanticLabel ?? text,
      hint: semanticHint,
      button: true,
      enabled: onPressed != null && !isLoading,
      child: button,
    );
  }
}

/// Responsive and accessible text field
class ResponsiveTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final int? maxLines;
  final String? semanticLabel;
  final String? semanticHint;

  const ResponsiveTextField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.errorText,
    this.obscureText = false,
    this.keyboardType,
    this.onChanged,
    this.onTap,
    this.prefixIcon,
    this.suffixIcon,
    this.maxLines = 1,
    this.semanticLabel,
    this.semanticHint,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final accessibilityService = AccessibilityService.to;
    
    final fontSize = ResponsiveUtils.responsiveFontSize(
      context,
      mobile: 16,
      tablet: 18,
      desktop: 20,
    );

    return Semantics(
      label: semanticLabel ?? labelText,
      hint: semanticHint ?? hintText,
      textField: true,
      child: TextField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        onChanged: onChanged,
        onTap: onTap,
        maxLines: maxLines,
        style: accessibilityService.getAccessibleTextStyle(
          TextStyle(fontSize: fontSize),
        ),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          errorText: errorText,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          contentPadding: ResponsiveUtils.responsive(
            context,
            mobile: const EdgeInsets.all(16),
            tablet: const EdgeInsets.all(18),
            desktop: const EdgeInsets.all(20),
          ),
        ),
      ),
    );
  }
}

/// Responsive and accessible list tile
class ResponsiveListTile extends StatelessWidget {
  final Widget? leading;
  final Widget title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool selected;
  final String? semanticLabel;
  final String? semanticHint;

  const ResponsiveListTile({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.selected = false,
    this.semanticLabel,
    this.semanticHint,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final accessibilityService = AccessibilityService.to;
    
    final titleFontSize = ResponsiveUtils.responsiveFontSize(
      context,
      mobile: 16,
      tablet: 18,
      desktop: 20,
    );
    
    final subtitleFontSize = ResponsiveUtils.responsiveFontSize(
      context,
      mobile: 14,
      tablet: 16,
      desktop: 18,
    );

    final contentPadding = ResponsiveUtils.responsive(
      context,
      mobile: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      tablet: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      desktop: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    );

    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: onTap != null,
      selected: selected,
      child: ListTile(
        leading: leading,
        title: DefaultTextStyle(
          style: accessibilityService.getAccessibleTextStyle(
            TextStyle(fontSize: titleFontSize),
          ),
          child: title,
        ),
        subtitle: subtitle != null
            ? DefaultTextStyle(
                style: accessibilityService.getAccessibleTextStyle(
                  TextStyle(fontSize: subtitleFontSize),
                ),
                child: subtitle!,
              )
            : null,
        trailing: trailing,
        onTap: onTap,
        selected: selected,
        contentPadding: contentPadding,
      ),
    );
  }
}

/// Responsive grid view
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double? spacing;
  final double? runSpacing;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final EdgeInsets? padding;

  const ResponsiveGridView({
    super.key,
    required this.children,
    this.spacing,
    this.runSpacing,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.physics,
    this.shrinkWrap = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveUtils.responsive(
      context,
      mobile: mobileColumns ?? 1,
      tablet: tabletColumns ?? 2,
      desktop: desktopColumns ?? 3,
    );

    final gridSpacing = spacing ?? ResponsiveUtils.responsiveSpacing(context);
    final gridRunSpacing = runSpacing ?? ResponsiveUtils.responsiveSpacing(context);
    final gridPadding = padding ?? ResponsiveUtils.responsivePadding(context);

    return GridView.count(
      crossAxisCount: columns,
      crossAxisSpacing: gridSpacing,
      mainAxisSpacing: gridRunSpacing,
      padding: gridPadding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      children: children,
    );
  }
}

/// Responsive app bar
class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final String? semanticLabel;

  const ResponsiveAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final accessibilityService = AccessibilityService.to;
    
    final titleFontSize = ResponsiveUtils.responsiveFontSize(
      context,
      mobile: 20,
      tablet: 22,
      desktop: 24,
    );

    return Semantics(
      label: semanticLabel ?? 'App bar, $title',
      header: true,
      child: AppBar(
        title: Text(
          title,
          style: accessibilityService.getAccessibleTextStyle(
            TextStyle(
              fontSize: titleFontSize,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        actions: actions,
        leading: leading,
        centerTitle: centerTitle,
        backgroundColor: backgroundColor ?? Colors.transparent,
        elevation: 0,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Responsive bottom navigation bar
class ResponsiveBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;
  final List<BottomNavigationBarItem> items;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;

  const ResponsiveBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final accessibilityService = AccessibilityService.to;
    
    final fontSize = ResponsiveUtils.responsiveFontSize(
      context,
      mobile: 12,
      tablet: 14,
      desktop: 16,
    );

    return Semantics(
      label: 'Bottom navigation',
      child: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: (index) {
          accessibilityService.provideAccessibilityFeedback(
            AccessibilityFeedbackType.selection,
          );
          onTap(index);
        },
        items: items.map((item) {
          return BottomNavigationBarItem(
            icon: Semantics(
              label: item.label,
              button: true,
              child: item.icon,
            ),
            label: item.label,
          );
        }).toList(),
        backgroundColor: backgroundColor,
        selectedItemColor: selectedItemColor,
        unselectedItemColor: unselectedItemColor,
        selectedLabelStyle: accessibilityService.getAccessibleTextStyle(
          TextStyle(fontSize: fontSize),
        ),
        unselectedLabelStyle: accessibilityService.getAccessibleTextStyle(
          TextStyle(fontSize: fontSize),
        ),
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}
