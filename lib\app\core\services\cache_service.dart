import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'storage_service.dart';

/// Comprehensive caching service for API responses, images, and data
class CacheService extends GetxService {
  static CacheService get to => Get.find();

  late Directory _cacheDirectory;
  late Directory _imageDirectory;
  
  // In-memory cache for frequently accessed data
  final Map<String, CacheItem> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  
  // Cache configuration
  static const int maxMemoryCacheSize = 100; // Maximum items in memory
  static const Duration defaultCacheDuration = Duration(hours: 1);
  static const Duration imageCacheDuration = Duration(days: 7);
  static const Duration apiCacheDuration = Duration(minutes: 30);

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeDirectories();
    await _cleanExpiredCache();
  }

  /// Initialize cache directories
  Future<void> _initializeDirectories() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDir.path}/cache');
      _imageDirectory = Directory('${appDir.path}/cache/images');
      
      if (!await _cacheDirectory.exists()) {
        await _cacheDirectory.create(recursive: true);
      }
      
      if (!await _imageDirectory.exists()) {
        await _imageDirectory.create(recursive: true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing cache directories: $e');
      }
    }
  }

  /// Store data in cache with expiration
  Future<void> put<T>(
    String key,
    T data, {
    Duration? duration,
    CacheType type = CacheType.memory,
  }) async {
    final expiration = DateTime.now().add(duration ?? defaultCacheDuration);
    final cacheItem = CacheItem<T>(
      data: data,
      expiration: expiration,
      type: type,
    );

    // Store in memory cache
    if (type == CacheType.memory || type == CacheType.both) {
      _memoryCache[key] = cacheItem;
      _cacheTimestamps[key] = DateTime.now();
      _enforceMemoryCacheLimit();
    }

    // Store in persistent cache
    if (type == CacheType.persistent || type == CacheType.both) {
      await _storePersistent(key, cacheItem);
    }
  }

  /// Get data from cache
  Future<T?> get<T>(String key) async {
    // Check memory cache first
    if (_memoryCache.containsKey(key)) {
      final item = _memoryCache[key] as CacheItem<T>?;
      if (item != null && !item.isExpired) {
        return item.data;
      } else {
        _memoryCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }

    // Check persistent cache
    final persistentItem = await _getPersistent<T>(key);
    if (persistentItem != null && !persistentItem.isExpired) {
      // Move to memory cache for faster access
      _memoryCache[key] = persistentItem;
      _cacheTimestamps[key] = DateTime.now();
      return persistentItem.data;
    }

    return null;
  }

  /// Check if cache contains key and is not expired
  Future<bool> contains(String key) async {
    final item = await get(key);
    return item != null;
  }

  /// Remove item from cache
  Future<void> remove(String key) async {
    _memoryCache.remove(key);
    _cacheTimestamps.remove(key);
    
    final file = File('${_cacheDirectory.path}/$key.cache');
    if (await file.exists()) {
      await file.delete();
    }
  }

  /// Clear all cache
  Future<void> clear() async {
    _memoryCache.clear();
    _cacheTimestamps.clear();
    
    if (await _cacheDirectory.exists()) {
      await _cacheDirectory.delete(recursive: true);
      await _initializeDirectories();
    }
  }

  /// Get cache size in bytes
  Future<int> getCacheSize() async {
    int size = 0;
    
    if (await _cacheDirectory.exists()) {
      await for (final entity in _cacheDirectory.list(recursive: true)) {
        if (entity is File) {
          size += await entity.length();
        }
      }
    }
    
    return size;
  }

  /// Get formatted cache size
  Future<String> getFormattedCacheSize() async {
    final size = await getCacheSize();
    return _formatBytes(size);
  }

  /// Cache API response
  Future<void> cacheApiResponse(
    String endpoint,
    Map<String, dynamic> response, {
    Duration? duration,
  }) async {
    final key = 'api_${endpoint.replaceAll('/', '_')}';
    await put(
      key,
      response,
      duration: duration ?? apiCacheDuration,
      type: CacheType.both,
    );
  }

  /// Get cached API response
  Future<Map<String, dynamic>?> getCachedApiResponse(String endpoint) async {
    final key = 'api_${endpoint.replaceAll('/', '_')}';
    return await get<Map<String, dynamic>>(key);
  }

  /// Cache image file
  Future<String?> cacheImage(String url, List<int> imageBytes) async {
    try {
      final key = _generateImageKey(url);
      final file = File('${_imageDirectory.path}/$key');
      
      await file.writeAsBytes(imageBytes);
      
      // Store metadata
      await put(
        'image_meta_$key',
        {
          'url': url,
          'path': file.path,
          'size': imageBytes.length,
        },
        duration: imageCacheDuration,
        type: CacheType.persistent,
      );
      
      return file.path;
    } catch (e) {
      if (kDebugMode) {
        print('Error caching image: $e');
      }
      return null;
    }
  }

  /// Get cached image path
  Future<String?> getCachedImagePath(String url) async {
    final key = _generateImageKey(url);
    final metadata = await get<Map<String, dynamic>>('image_meta_$key');
    
    if (metadata != null) {
      final path = metadata['path'] as String?;
      if (path != null) {
        final file = File(path);
        if (await file.exists()) {
          return path;
        }
      }
    }
    
    return null;
  }

  /// Store persistent cache item
  Future<void> _storePersistent<T>(String key, CacheItem<T> item) async {
    try {
      final file = File('${_cacheDirectory.path}/$key.cache');
      final json = {
        'data': item.data,
        'expiration': item.expiration.millisecondsSinceEpoch,
        'type': item.type.index,
      };
      await file.writeAsString(jsonEncode(json));
    } catch (e) {
      if (kDebugMode) {
        print('Error storing persistent cache: $e');
      }
    }
  }

  /// Get persistent cache item
  Future<CacheItem<T>?> _getPersistent<T>(String key) async {
    try {
      final file = File('${_cacheDirectory.path}/$key.cache');
      if (!await file.exists()) return null;
      
      final content = await file.readAsString();
      final json = jsonDecode(content) as Map<String, dynamic>;
      
      return CacheItem<T>(
        data: json['data'] as T,
        expiration: DateTime.fromMillisecondsSinceEpoch(json['expiration']),
        type: CacheType.values[json['type']],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting persistent cache: $e');
      }
      return null;
    }
  }

  /// Enforce memory cache size limit
  void _enforceMemoryCacheLimit() {
    if (_memoryCache.length > maxMemoryCacheSize) {
      // Remove oldest items
      final sortedKeys = _cacheTimestamps.keys.toList()
        ..sort((a, b) => _cacheTimestamps[a]!.compareTo(_cacheTimestamps[b]!));
      
      final keysToRemove = sortedKeys.take(_memoryCache.length - maxMemoryCacheSize);
      for (final key in keysToRemove) {
        _memoryCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
  }

  /// Clean expired cache items
  Future<void> _cleanExpiredCache() async {
    try {
      // Clean memory cache
      final expiredKeys = <String>[];
      for (final entry in _memoryCache.entries) {
        if (entry.value.isExpired) {
          expiredKeys.add(entry.key);
        }
      }
      for (final key in expiredKeys) {
        _memoryCache.remove(key);
        _cacheTimestamps.remove(key);
      }

      // Clean persistent cache
      if (await _cacheDirectory.exists()) {
        await for (final entity in _cacheDirectory.list()) {
          if (entity is File && entity.path.endsWith('.cache')) {
            try {
              final content = await entity.readAsString();
              final json = jsonDecode(content) as Map<String, dynamic>;
              final expiration = DateTime.fromMillisecondsSinceEpoch(json['expiration']);
              
              if (DateTime.now().isAfter(expiration)) {
                await entity.delete();
              }
            } catch (e) {
              // If we can't read the file, delete it
              await entity.delete();
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cleaning expired cache: $e');
      }
    }
  }

  /// Generate image cache key from URL
  String _generateImageKey(String url) {
    return url.hashCode.abs().toString();
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// Cache item wrapper
class CacheItem<T> {
  final T data;
  final DateTime expiration;
  final CacheType type;

  CacheItem({
    required this.data,
    required this.expiration,
    required this.type,
  });

  bool get isExpired => DateTime.now().isAfter(expiration);
}

/// Cache storage types
enum CacheType {
  memory,     // Store only in memory (fast but temporary)
  persistent, // Store only on disk (slower but survives app restart)
  both,       // Store in both memory and disk (best of both worlds)
}
