import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/widgets/flat_list_tile.dart';
import '../../../shared/widgets/flat_button.dart';
import 'privacy_settings_controller.dart';

class PrivacySettingsView extends GetView<PrivacySettingsController> {
  const PrivacySettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Privacy Settings',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Data Collection Section
            _buildSectionHeader(context, 'Data Collection'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.data_usage,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  title: 'Data Collection',
                  subtitle: 'Allow collection of usage data',
                  trailing: Switch(
                    value: controller.dataCollection.value,
                    onChanged: (_) => controller.toggleDataCollection(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleDataCollection,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.analytics,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ),
                  title: 'Analytics Tracking',
                  subtitle: 'Help improve the app with usage analytics',
                  trailing: Switch(
                    value: controller.analyticsTracking.value,
                    onChanged: (_) => controller.toggleAnalyticsTracking(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleAnalyticsTracking,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.bug_report,
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                  title: 'Crash Reporting',
                  subtitle: 'Send crash reports to help fix issues',
                  trailing: Switch(
                    value: controller.crashReporting.value,
                    onChanged: (_) => controller.toggleCrashReporting(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleCrashReporting,
                )),

            const SizedBox(height: 24),

            // Advertising Section
            _buildSectionHeader(context, 'Advertising'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.ads_click,
                      color: Colors.purple,
                      size: 20,
                    ),
                  ),
                  title: 'Personalized Ads',
                  subtitle: 'Show ads based on your interests',
                  trailing: Switch(
                    value: controller.personalizedAds.value,
                    onChanged: (_) => controller.togglePersonalizedAds(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.togglePersonalizedAds,
                )),

            const SizedBox(height: 24),

            // Permissions Section
            _buildSectionHeader(context, 'App Permissions'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.location_on,
                      color: Colors.green,
                      size: 20,
                    ),
                  ),
                  title: 'Location Access',
                  subtitle: 'Allow access to your location',
                  trailing: Switch(
                    value: controller.locationTracking.value,
                    onChanged: (_) => controller.toggleLocationTracking(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleLocationTracking,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.teal.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.contacts,
                      color: Colors.teal,
                      size: 20,
                    ),
                  ),
                  title: 'Contacts Access',
                  subtitle: 'Allow access to your contacts',
                  trailing: Switch(
                    value: controller.contactsAccess.value,
                    onChanged: (_) => controller.toggleContactsAccess(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleContactsAccess,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.indigo.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.indigo,
                      size: 20,
                    ),
                  ),
                  title: 'Camera Access',
                  subtitle: 'Allow access to your camera',
                  trailing: Switch(
                    value: controller.cameraAccess.value,
                    onChanged: (_) => controller.toggleCameraAccess(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleCameraAccess,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.pink.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.mic,
                      color: Colors.pink,
                      size: 20,
                    ),
                  ),
                  title: 'Microphone Access',
                  subtitle: 'Allow access to your microphone',
                  trailing: Switch(
                    value: controller.microphoneAccess.value,
                    onChanged: (_) => controller.toggleMicrophoneAccess(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleMicrophoneAccess,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.cyan.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.storage,
                      color: Colors.cyan,
                      size: 20,
                    ),
                  ),
                  title: 'Storage Access',
                  subtitle: 'Allow access to device storage',
                  trailing: Switch(
                    value: controller.storageAccess.value,
                    onChanged: (_) => controller.toggleStorageAccess(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleStorageAccess,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.amber.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.fingerprint,
                      color: Colors.amber,
                      size: 20,
                    ),
                  ),
                  title: 'Biometric Data',
                  subtitle: 'Allow collection of biometric data',
                  trailing: Switch(
                    value: controller.biometricData.value,
                    onChanged: (_) => controller.toggleBiometricData(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleBiometricData,
                )),

            const SizedBox(height: 24),

            // Data Sharing Section
            _buildSectionHeader(context, 'Data Sharing'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.deepOrange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.share,
                      color: Colors.deepOrange,
                      size: 20,
                    ),
                  ),
                  title: 'Share Usage Data',
                  subtitle: 'Share anonymized usage data',
                  trailing: Switch(
                    value: controller.shareUsageData.value,
                    onChanged: (_) => controller.toggleShareUsageData(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleShareUsageData,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.brown.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.integration_instructions,
                      color: Colors.brown,
                      size: 20,
                    ),
                  ),
                  title: 'Third-Party Integration',
                  subtitle: 'Allow data sharing with partners',
                  trailing: Switch(
                    value: controller.thirdPartyIntegration.value,
                    onChanged: (_) => controller.toggleThirdPartyIntegration(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleThirdPartyIntegration,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.cookie,
                      color: Colors.grey,
                      size: 20,
                    ),
                  ),
                  title: 'Cookie Consent',
                  subtitle: 'Allow cookies for better experience',
                  trailing: Switch(
                    value: controller.cookieConsent.value,
                    onChanged: (_) => controller.toggleCookieConsent(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleCookieConsent,
                )),

            const SizedBox(height: 24),

            // Data Retention Section
            _buildSectionHeader(context, 'Data Retention'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.deepPurple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.schedule,
                      color: Colors.deepPurple,
                      size: 20,
                    ),
                  ),
                  title: 'Data Retention Period',
                  subtitle: controller.dataRetentionPeriod.value,
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  onTap: controller.changeDataRetentionPeriod,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.auto_delete,
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                  title: 'Auto Delete Data',
                  subtitle: 'Automatically delete old data',
                  trailing: Switch(
                    value: controller.autoDeleteData.value,
                    onChanged: (_) => controller.toggleAutoDeleteData(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleAutoDeleteData,
                )),

            const SizedBox(height: 24),

            // Privacy Actions Section
            _buildSectionHeader(context, 'Privacy Actions'),
            const SizedBox(height: 12),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.policy,
                  color: Colors.blue,
                  size: 20,
                ),
              ),
              title: 'Privacy Policy',
              subtitle: 'Read our privacy policy',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.viewPrivacyPolicy,
            ),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.download,
                  color: Colors.green,
                  size: 20,
                ),
              ),
              title: 'Download Privacy Report',
              subtitle: 'Get a report of your privacy settings',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.downloadPrivacyReport,
            ),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.delete_forever,
                  color: Colors.red,
                  size: 20,
                ),
              ),
              title: 'Request Data Deletion',
              subtitle: 'Delete all your personal data',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.requestDataDeletion,
            ),

            const SizedBox(height: 32),

            // Reset Button
            FlatButton(
              text: 'Reset All Privacy Settings',
              onPressed: controller.resetAllPrivacySettings,
              backgroundColor: Colors.orange.withValues(alpha: 0.1),
              textColor: Colors.orange,
              outlined: true,
              borderColor: Colors.orange.withValues(alpha: 0.3),
              width: double.infinity,
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
        color: theme.colorScheme.onSurface,
      ),
    );
  }
}
