import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AboutController extends GetxController {
  final RxString appName = 'Nexed Mini'.obs;
  final RxString version = '1.0.0'.obs;
  final RxString buildNumber = '1'.obs;
  final RxBool isLoading = true.obs;

  @override
  void onInit() {
    super.onInit();
    _loadAppInfo();
  }

  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      appName.value = packageInfo.appName;
      version.value = packageInfo.version;
      buildNumber.value = packageInfo.buildNumber;
    } catch (e) {
      // Use default values if package info fails
      appName.value = 'Nexed Mini';
      version.value = '1.0.0';
      buildNumber.value = '1';
    } finally {
      isLoading.value = false;
    }
  }

  void openPrivacyPolicy() {
    Get.snackbar(
      'Privacy Policy',
      'Privacy policy feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void openTermsOfService() {
    Get.snackbar(
      'Terms of Service',
      'Terms of service feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void openLicenses() {
    Get.snackbar(
      'Licenses',
      'Open source licenses feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void contactSupport() {
    Get.snackbar(
      'Contact Support',
      'Support contact feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void rateApp() {
    Get.snackbar(
      'Rate App',
      'App rating feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }
}
