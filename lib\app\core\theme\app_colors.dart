import 'package:flutter/material.dart';

class AppColors {
  // Primary Pastel Colors
  static const Color primaryPastel = Color(0xFFB8A9FF);
  static const Color secondaryPastel = Color(0xFFFFA9E7);
  static const Color accentPastel = Color(0xFFA9FFFF);
  static const Color backgroundPastel = Color(0xFFF5F3FF);

  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFFB8A9FF),
    Color(0xFFFFA9E7),
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFFA9FFFF),
    Color(0xFFB8A9FF),
  ];

  static const List<Color> backgroundGradient = [
    Color(0xFFF5F3FF),
    Color(0xFFE8F4FD),
  ];

  // Text Colors
  static const Color textPrimary = Color(0xFF2D3748);
  static const Color textSecondary = Color(0xFF718096);
  static const Color textLight = Color(0xFFA0AEC0);

  // Status Colors
  static const Color success = Color(0xFF68D391);
  static const Color error = Color(0xFFF56565);
  static const Color warning = Color(0xFFED8936);
  static const Color info = Color(0xFF63B3ED);

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
}
