import 'package:json_annotation/json_annotation.dart';

part 'notification_model.g.dart';

enum NotificationType {
  @JsonValue('assignment')
  assignment,
  @JsonValue('cafeteria')
  cafeteria,
  @JsonValue('library')
  library,
  @JsonValue('sports')
  sports,
  @JsonValue('grade')
  grade,
  @JsonValue('announcement')
  announcement,
}

@JsonSerializable()
class NotificationItem {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime time;
  final bool isRead;
  final String? actionUrl;
  final Map<String, dynamic>? data;

  const NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.time,
    required this.isRead,
    this.actionUrl,
    this.data,
  });

  factory NotificationItem.fromJson(Map<String, dynamic> json) => _$NotificationItemFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationItemToJson(this);

  NotificationItem copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? time,
    bool? isRead,
    String? actionUrl,
    Map<String, dynamic>? data,
  }) {
    return NotificationItem(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      time: time ?? this.time,
      isRead: isRead ?? this.isRead,
      actionUrl: actionUrl ?? this.actionUrl,
      data: data ?? this.data,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationItem &&
        other.id == id &&
        other.title == title &&
        other.message == message &&
        other.type == type &&
        other.time == time &&
        other.isRead == isRead &&
        other.actionUrl == actionUrl &&
        other.data == data;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        message.hashCode ^
        type.hashCode ^
        time.hashCode ^
        isRead.hashCode ^
        actionUrl.hashCode ^
        data.hashCode;
  }

  @override
  String toString() {
    return 'NotificationItem(id: $id, title: $title, message: $message, type: $type, time: $time, isRead: $isRead, actionUrl: $actionUrl, data: $data)';
  }
}
