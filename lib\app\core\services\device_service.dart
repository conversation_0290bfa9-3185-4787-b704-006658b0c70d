import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

class DeviceService extends GetxService {
  static DeviceService get to => Get.find();

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  final Connectivity _connectivity = Connectivity();

  late PackageInfo _packageInfo;
  late Map<String, dynamic> _deviceData;

  final RxBool isConnected = true.obs;
  final Rx<ConnectivityResult> connectionType = ConnectivityResult.none.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeDeviceInfo();
    await _initializePackageInfo();
    _initializeConnectivityListener();
  }

  Future<void> _initializeDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        _deviceData = {
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'id': androidInfo.id,
          'isPhysicalDevice': androidInfo.isPhysicalDevice,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        _deviceData = {
          'platform': 'iOS',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'localizedModel': iosInfo.localizedModel,
          'identifierForVendor': iosInfo.identifierForVendor,
          'isPhysicalDevice': iosInfo.isPhysicalDevice,
        };
      } else {
        _deviceData = {
          'platform': Platform.operatingSystem,
          'version': Platform.operatingSystemVersion,
        };
      }
    } catch (e) {
      print('Error getting device info: $e');
      _deviceData = {
        'platform': Platform.operatingSystem,
        'error': 'Failed to get device info',
      };
    }
  }

  Future<void> _initializePackageInfo() async {
    try {
      _packageInfo = await PackageInfo.fromPlatform();
    } catch (e) {
      print('Error getting package info: $e');
    }
  }

  void _initializeConnectivityListener() {
    _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
      connectionType.value = result;
      isConnected.value = result != ConnectivityResult.none;
    });

    // Check initial connectivity
    _checkConnectivity();
  }

  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      connectionType.value = result;
      isConnected.value = result != ConnectivityResult.none;
    } catch (e) {
      print('Error checking connectivity: $e');
    }
  }

  // Device Information Getters
  String get platform => _deviceData['platform'] ?? 'Unknown';
  String get deviceModel => _deviceData['model'] ?? 'Unknown';
  String get deviceManufacturer => _deviceData['manufacturer'] ?? 'Unknown';
  String get osVersion => _deviceData['version'] ?? 'Unknown';
  bool get isPhysicalDevice => _deviceData['isPhysicalDevice'] ?? true;

  // iOS specific
  String get deviceName => _deviceData['name'] ?? 'Unknown';
  String get systemName => _deviceData['systemName'] ?? 'Unknown';
  String get systemVersion => _deviceData['systemVersion'] ?? 'Unknown';

  // Android specific
  int get sdkInt => _deviceData['sdkInt'] ?? 0;
  String get brand => _deviceData['brand'] ?? 'Unknown';
  String get deviceId => _deviceData['id'] ?? 'Unknown';

  // App Information Getters
  String get appName => _packageInfo.appName;
  String get packageName => _packageInfo.packageName;
  String get version => _packageInfo.version;
  String get buildNumber => _packageInfo.buildNumber;

  // Connectivity Information
  bool get hasInternetConnection => isConnected.value;
  String get connectionTypeString {
    switch (connectionType.value) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return 'Other';
      case ConnectivityResult.none:
      default:
        return 'No Connection';
    }
  }

  // Device Capabilities
  bool get isAndroid => Platform.isAndroid;
  bool get isIOS => Platform.isIOS;
  bool get isMobile => Platform.isAndroid || Platform.isIOS;
  bool get isDesktop => Platform.isWindows || Platform.isMacOS || Platform.isLinux;

  // Utility Methods
  Map<String, dynamic> getDeviceInfo() {
    return {
      'platform': platform,
      'model': deviceModel,
      'manufacturer': deviceManufacturer,
      'osVersion': osVersion,
      'isPhysicalDevice': isPhysicalDevice,
      'appName': appName,
      'appVersion': version,
      'buildNumber': buildNumber,
      'connectionType': connectionTypeString,
      'hasInternet': hasInternetConnection,
    };
  }

  String getDeviceIdentifier() {
    if (Platform.isAndroid) {
      return deviceId;
    } else if (Platform.isIOS) {
      return _deviceData['identifierForVendor'] ?? 'Unknown';
    }
    return 'Unknown';
  }

  String getUserAgent() {
    return '$appName/$version ($platform $osVersion; $deviceModel)';
  }

  bool isVersionGreaterThan(String targetVersion) {
    try {
      final currentParts = version.split('.').map(int.parse).toList();
      final targetParts = targetVersion.split('.').map(int.parse).toList();

      for (int i = 0; i < currentParts.length && i < targetParts.length; i++) {
        if (currentParts[i] > targetParts[i]) return true;
        if (currentParts[i] < targetParts[i]) return false;
      }

      return currentParts.length > targetParts.length;
    } catch (e) {
      return false;
    }
  }

  Future<void> refreshConnectivity() async {
    await _checkConnectivity();
  }

  // Debug Information
  void printDeviceInfo() {
    print('=== Device Information ===');
    print('Platform: $platform');
    print('Model: $deviceModel');
    print('Manufacturer: $deviceManufacturer');
    print('OS Version: $osVersion');
    print('Is Physical Device: $isPhysicalDevice');
    print('App Name: $appName');
    print('App Version: $version');
    print('Build Number: $buildNumber');
    print('Connection Type: $connectionTypeString');
    print('Has Internet: $hasInternetConnection');
    print('========================');
  }
}
