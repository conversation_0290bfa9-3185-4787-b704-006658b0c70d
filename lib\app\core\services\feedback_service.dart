import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// Centralized user feedback service for consistent UI feedback
class FeedbackService extends GetxService {
  static FeedbackService get to => Get.find();

  /// Show success feedback
  void showSuccess({
    required String message,
    String? title,
    Duration? duration,
    VoidCallback? onTap,
  }) {
    _showSnackbar(
      title: title ?? 'Success',
      message: message,
      backgroundColor: Colors.green,
      icon: Icons.check_circle,
      duration: duration,
      onTap: onTap,
    );
  }

  /// Show error feedback
  void showError({
    required String message,
    String? title,
    Duration? duration,
    VoidCallback? onTap,
    VoidCallback? onRetry,
  }) {
    _showSnackbar(
      title: title ?? 'Error',
      message: message,
      backgroundColor: Colors.red,
      icon: Icons.error,
      duration: duration,
      onTap: onTap,
      action: onRetry != null
          ? SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: onRetry,
            )
          : null,
    );
  }

  /// Show warning feedback
  void showWarning({
    required String message,
    String? title,
    Duration? duration,
    VoidCallback? onTap,
  }) {
    _showSnackbar(
      title: title ?? 'Warning',
      message: message,
      backgroundColor: Colors.orange,
      icon: Icons.warning,
      duration: duration,
      onTap: onTap,
    );
  }

  /// Show info feedback
  void showInfo({
    required String message,
    String? title,
    Duration? duration,
    VoidCallback? onTap,
  }) {
    _showSnackbar(
      title: title ?? 'Info',
      message: message,
      backgroundColor: Colors.blue,
      icon: Icons.info,
      duration: duration,
      onTap: onTap,
    );
  }

  /// Show loading feedback
  void showLoading({
    required String message,
    String? title,
  }) {
    Get.snackbar(
      title ?? 'Loading',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.withOpacity(0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.hourglass_empty, color: Colors.white),
      showProgressIndicator: true,
      duration: const Duration(minutes: 5), // Long duration for loading
      isDismissible: false,
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
    );
  }

  /// Hide current snackbar
  void hideSnackbar() {
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
  }

  /// Show confirmation dialog
  Future<bool> showConfirmation({
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    Color? confirmColor,
    IconData? icon,
  }) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: Row(
          children: [
            if (icon != null) ...[
              Icon(icon, color: confirmColor),
              const SizedBox(width: 8),
            ],
            Expanded(child: Text(title)),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: confirmColor != null
                ? TextButton.styleFrom(foregroundColor: confirmColor)
                : null,
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// Show input dialog
  Future<String?> showInputDialog({
    required String title,
    String? message,
    String? hintText,
    String? initialValue,
    String confirmText = 'OK',
    String cancelText = 'Cancel',
    TextInputType? keyboardType,
    int? maxLength,
    String? Function(String?)? validator,
  }) async {
    final controller = TextEditingController(text: initialValue);
    final formKey = GlobalKey<FormState>();

    final result = await Get.dialog<String>(
      AlertDialog(
        title: Text(title),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (message != null) ...[
                Text(message),
                const SizedBox(height: 16),
              ],
              TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: hintText,
                  border: const OutlineInputBorder(),
                ),
                keyboardType: keyboardType,
                maxLength: maxLength,
                validator: validator,
                autofocus: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () {
              if (formKey.currentState?.validate() ?? true) {
                Get.back(result: controller.text);
              }
            },
            child: Text(confirmText),
          ),
        ],
      ),
    );

    controller.dispose();
    return result;
  }

  /// Show selection dialog
  Future<T?> showSelectionDialog<T>({
    required String title,
    required List<SelectionItem<T>> items,
    T? selectedValue,
    String cancelText = 'Cancel',
  }) async {
    return await Get.dialog<T>(
      AlertDialog(
        title: Text(title),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              return RadioListTile<T>(
                title: Text(item.label),
                subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
                value: item.value,
                groupValue: selectedValue,
                onChanged: (value) => Get.back(result: value),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(cancelText),
          ),
        ],
      ),
    );
  }

  /// Show bottom sheet with options
  Future<T?> showBottomSheetOptions<T>({
    required String title,
    required List<BottomSheetOption<T>> options,
    String? subtitle,
  }) async {
    return await Get.bottomSheet<T>(
      Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Title
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            
            // Options
            ...options.map((option) => ListTile(
                  leading: option.icon != null
                      ? Icon(option.icon, color: option.color)
                      : null,
                  title: Text(option.label),
                  subtitle: option.subtitle != null
                      ? Text(option.subtitle!)
                      : null,
                  onTap: () => Get.back(result: option.value),
                )),
            
            // Safe area padding
            SizedBox(height: MediaQuery.of(Get.context!).padding.bottom),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  /// Provide haptic feedback
  void hapticFeedback(HapticFeedbackType type) {
    switch (type) {
      case HapticFeedbackType.light:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.medium:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavy:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
    }
  }

  /// Private method to show snackbar with consistent styling
  void _showSnackbar({
    required String title,
    required String message,
    required Color backgroundColor,
    required IconData icon,
    Duration? duration,
    VoidCallback? onTap,
    SnackBarAction? action,
  }) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: backgroundColor.withOpacity(0.9),
      colorText: Colors.white,
      icon: Icon(icon, color: Colors.white),
      duration: duration ?? const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
      onTap: onTap != null ? (_) => onTap() : null,
      mainButton: action != null
          ? TextButton(
              onPressed: action.onPressed,
              child: Text(
                action.label,
                style: TextStyle(color: action.textColor),
              ),
            )
          : null,
    );
  }
}

/// Selection item for dialogs
class SelectionItem<T> {
  final String label;
  final String? subtitle;
  final T value;

  SelectionItem({
    required this.label,
    required this.value,
    this.subtitle,
  });
}

/// Bottom sheet option
class BottomSheetOption<T> {
  final String label;
  final String? subtitle;
  final T value;
  final IconData? icon;
  final Color? color;

  BottomSheetOption({
    required this.label,
    required this.value,
    this.subtitle,
    this.icon,
    this.color,
  });
}

/// Haptic feedback types
enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
}
