import 'package:flutter/material.dart';

class FlatButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final double borderWidth;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final bool isLoading;
  final IconData? icon;
  final bool outlined;

  const FlatButton({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.borderWidth = 1,
    this.borderRadius = 12,
    this.padding,
    this.width,
    this.height,
    this.isLoading = false,
    this.icon,
    this.outlined = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor = outlined
        ? Colors.transparent
        : backgroundColor ?? theme.colorScheme.primary;
    final effectiveTextColor = outlined
        ? textColor ?? theme.colorScheme.primary
        : textColor ?? theme.colorScheme.onPrimary;
    final effectiveBorderColor = outlined
        ? borderColor ?? theme.colorScheme.primary
        : borderColor;

    return SizedBox(
      width: width,
      height: height ?? 48,
      child: Material(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              border: outlined || effectiveBorderColor != null
                  ? Border.all(
                      color: effectiveBorderColor ?? theme.colorScheme.primary,
                      width: borderWidth,
                    )
                  : null,
            ),
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isLoading)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(effectiveTextColor),
                    ),
                  )
                else if (icon != null) ...[
                  Icon(
                    icon,
                    size: 18,
                    color: effectiveTextColor,
                  ),
                  const SizedBox(width: 8),
                ],
                if (!isLoading)
                  Text(
                    text,
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: effectiveTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
