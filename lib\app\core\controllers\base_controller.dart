import 'package:get/get.dart';
import '../services/error_handler_service.dart';
import '../services/loading_service.dart';
import '../services/feedback_service.dart';

/// Base controller with built-in error handling and loading state management
abstract class BaseController extends GetxController with LoadingStateMixin {
  // Services
  ErrorHandlerService get errorHandler => ErrorHandlerService.to;
  LoadingService get loadingService => LoadingService.to;
  FeedbackService get feedback => FeedbackService.to;

  // Loading states for different operations
  final RxMap<String, bool> _operationLoading = <String, bool>{}.obs;
  
  // Error states
  final RxString lastError = ''.obs;
  final RxBool hasError = false.obs;

  /// Check if a specific operation is loading
  bool isOperationLoading(String operation) {
    return _operationLoading[operation] ?? false;
  }

  /// Set loading state for a specific operation
  void setOperationLoading(String operation, bool loading) {
    _operationLoading[operation] = loading;
  }

  /// Execute an async operation with comprehensive error handling and loading state
  Future<T?> executeOperation<T>({
    required String operationName,
    required Future<T> Function() operation,
    String? loadingMessage,
    String? successMessage,
    bool showGlobalLoading = false,
    bool showSuccessMessage = false,
    bool handleErrors = true,
    VoidCallback? onSuccess,
    VoidCallback? onError,
  }) async {
    try {
      // Set loading state
      setOperationLoading(operationName, true);
      if (showGlobalLoading) {
        loadingService.showGlobalLoading(message: loadingMessage);
      }

      // Clear previous errors
      clearError();

      // Execute operation
      final result = await operation();

      // Handle success
      if (showSuccessMessage && successMessage != null) {
        feedback.showSuccess(message: successMessage);
      }
      onSuccess?.call();

      return result;
    } catch (error) {
      // Handle error
      setError(error.toString());
      
      if (handleErrors) {
        errorHandler.handleError(
          error,
          context: operationName,
          onRetry: () => executeOperation(
            operationName: operationName,
            operation: operation,
            loadingMessage: loadingMessage,
            successMessage: successMessage,
            showGlobalLoading: showGlobalLoading,
            showSuccessMessage: showSuccessMessage,
            handleErrors: handleErrors,
            onSuccess: onSuccess,
            onError: onError,
          ),
        );
      }
      
      onError?.call();
      return null;
    } finally {
      // Clear loading state
      setOperationLoading(operationName, false);
      if (showGlobalLoading) {
        loadingService.hideGlobalLoading();
      }
    }
  }

  /// Execute operation with only loading state (no error handling)
  Future<T?> executeWithLoading<T>({
    required String operationName,
    required Future<T> Function() operation,
    String? loadingMessage,
    bool showGlobalLoading = false,
  }) async {
    try {
      setOperationLoading(operationName, true);
      if (showGlobalLoading) {
        loadingService.showGlobalLoading(message: loadingMessage);
      }

      return await operation();
    } finally {
      setOperationLoading(operationName, false);
      if (showGlobalLoading) {
        loadingService.hideGlobalLoading();
      }
    }
  }

  /// Set error state
  void setError(String error) {
    lastError.value = error;
    hasError.value = true;
  }

  /// Clear error state
  void clearError() {
    lastError.value = '';
    hasError.value = false;
  }

  /// Show success message
  void showSuccess(String message, {String? title}) {
    feedback.showSuccess(message: message, title: title);
  }

  /// Show error message
  void showError(String message, {String? title, VoidCallback? onRetry}) {
    feedback.showError(message: message, title: title, onRetry: onRetry);
  }

  /// Show warning message
  void showWarning(String message, {String? title}) {
    feedback.showWarning(message: message, title: title);
  }

  /// Show info message
  void showInfo(String message, {String? title}) {
    feedback.showInfo(message: message, title: title);
  }

  /// Show confirmation dialog
  Future<bool> showConfirmation({
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) async {
    return await feedback.showConfirmation(
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: cancelText,
    );
  }

  /// Retry last failed operation (to be implemented by subclasses)
  void retryLastOperation() {
    // Override in subclasses to implement retry logic
  }

  /// Refresh data (to be implemented by subclasses)
  Future<void> refreshData() async {
    // Override in subclasses to implement refresh logic
  }

  /// Handle network errors specifically
  void handleNetworkError(dynamic error) {
    errorHandler.handleError(
      error,
      context: runtimeType.toString(),
      onRetry: retryLastOperation,
    );
  }

  /// Check if any operation is currently loading
  bool get isAnyOperationLoading {
    return _operationLoading.values.any((loading) => loading);
  }

  /// Get all currently loading operations
  List<String> get loadingOperations {
    return _operationLoading.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
  }

  @override
  void onClose() {
    // Clear any global loading states
    loadingService.hideGlobalLoading();
    super.onClose();
  }
}

/// Mixin for controllers that need pagination support
mixin PaginationMixin<T> on BaseController {
  final RxList<T> items = <T>[].obs;
  final RxInt currentPage = 1.obs;
  final RxBool hasMoreData = true.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool isRefreshing = false.obs;

  int get itemsPerPage => 20;

  /// Load initial data
  Future<void> loadInitialData() async {
    await executeOperation(
      operationName: 'loadInitialData',
      operation: () async {
        currentPage.value = 1;
        hasMoreData.value = true;
        final newItems = await fetchData(1);
        items.assignAll(newItems);
        hasMoreData.value = newItems.length >= itemsPerPage;
      },
      showGlobalLoading: true,
      loadingMessage: 'Loading data...',
    );
  }

  /// Load more data for pagination
  Future<void> loadMoreData() async {
    if (isLoadingMore.value || !hasMoreData.value) return;

    isLoadingMore.value = true;
    try {
      final nextPage = currentPage.value + 1;
      final newItems = await fetchData(nextPage);
      
      if (newItems.isNotEmpty) {
        items.addAll(newItems);
        currentPage.value = nextPage;
        hasMoreData.value = newItems.length >= itemsPerPage;
      } else {
        hasMoreData.value = false;
      }
    } catch (error) {
      handleNetworkError(error);
    } finally {
      isLoadingMore.value = false;
    }
  }

  /// Refresh data
  @override
  Future<void> refreshData() async {
    isRefreshing.value = true;
    try {
      await loadInitialData();
    } finally {
      isRefreshing.value = false;
    }
  }

  /// Abstract method to fetch data (implement in subclasses)
  Future<List<T>> fetchData(int page);
}

/// Mixin for controllers that need search functionality
mixin SearchMixin<T> on BaseController {
  final RxString searchQuery = ''.obs;
  final RxList<T> searchResults = <T>[].obs;
  final RxBool isSearching = false.obs;

  /// Perform search
  Future<void> search(String query) async {
    searchQuery.value = query;
    
    if (query.isEmpty) {
      searchResults.clear();
      return;
    }

    await executeOperation(
      operationName: 'search',
      operation: () async {
        isSearching.value = true;
        final results = await performSearch(query);
        searchResults.assignAll(results);
      },
      handleErrors: true,
    );
    
    isSearching.value = false;
  }

  /// Clear search
  void clearSearch() {
    searchQuery.value = '';
    searchResults.clear();
  }

  /// Abstract method to perform search (implement in subclasses)
  Future<List<T>> performSearch(String query);
}
