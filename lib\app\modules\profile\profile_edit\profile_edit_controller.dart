import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/services/media_service.dart';
import '../../../data/services/auth_service.dart';
import '../../../routes/app_routes.dart';

class ProfileEditController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final bioController = TextEditingController();

  final RxBool isLoading = false.obs;
  final RxString profileImagePath = ''.obs;
  final RxString profileImageUrl = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _loadUserData();
  }

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    bioController.dispose();
    super.onClose();
  }

  void _loadUserData() {
    final userData = StorageService.to.userData;
    if (userData != null) {
      nameController.text = userData['name'] ?? '';
      emailController.text = userData['email'] ?? '';
      phoneController.text = userData['phone'] ?? '';
      bioController.text = userData['bio'] ?? '';
      profileImageUrl.value = userData['profileImageUrl'] ?? '';
    }
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your name';
    }
    if (value.length < 2) {
      return 'Name must be at least 2 characters';
    }
    return null;
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your email';
    }
    if (!GetUtils.isEmail(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? validatePhone(String? value) {
    if (value != null && value.isNotEmpty) {
      if (!GetUtils.isPhoneNumber(value)) {
        return 'Please enter a valid phone number';
      }
    }
    return null;
  }

  void selectProfileImage() {
    MediaService.to.showImagePickerDialog(
      onGallery: () async {
        final file = await MediaService.to.pickImageFromGallery();
        if (file != null) {
          profileImagePath.value = file.path;
        }
      },
      onCamera: () async {
        final file = await MediaService.to.pickImageFromCamera();
        if (file != null) {
          profileImagePath.value = file.path;
        }
      },
    );
  }

  void removeProfileImage() {
    profileImagePath.value = '';
    profileImageUrl.value = '';
  }

  Future<void> saveProfile() async {
    if (!formKey.currentState!.validate()) return;

    try {
      isLoading.value = true;

      // Update profile via API
      final updatedUser = await AuthService.to.updateProfile(
        name: nameController.text.trim(),
        email: emailController.text.trim(),
        phone: phoneController.text.trim().isEmpty ? null : phoneController.text.trim(),
        bio: bioController.text.trim().isEmpty ? null : bioController.text.trim(),
      );

      // Update local storage
      final currentUserData = StorageService.to.userData ?? {};
      final updatedUserData = {
        ...currentUserData,
        'name': updatedUser.name,
        'email': updatedUser.email,
        'phone': phoneController.text.trim(),
        'bio': bioController.text.trim(),
      };

      // Handle profile image if changed
      if (profileImagePath.value.isNotEmpty) {
        // In a real app, you would upload the image to your server here
        // For now, we'll just store the local path
        updatedUserData['profileImageUrl'] = profileImagePath.value;
      }

      StorageService.to.userData = updatedUserData;

      Get.snackbar(
        'Success',
        'Profile updated successfully!',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.withValues(alpha: 0.8),
        colorText: Colors.white,
      );

      Get.back(result: true);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update profile: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void goBack() {
    Get.back();
  }

  void showDiscardDialog() {
    if (_hasChanges()) {
      Get.dialog(
        AlertDialog(
          title: const Text('Discard Changes?'),
          content: const Text('You have unsaved changes. Are you sure you want to discard them?'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Get.back(); // Close dialog
                Get.back(); // Go back to profile
              },
              child: const Text('Discard'),
            ),
          ],
        ),
      );
    } else {
      Get.back();
    }
  }

  bool _hasChanges() {
    final userData = StorageService.to.userData;
    if (userData == null) return true;

    return nameController.text.trim() != (userData['name'] ?? '') ||
           emailController.text.trim() != (userData['email'] ?? '') ||
           phoneController.text.trim() != (userData['phone'] ?? '') ||
           bioController.text.trim() != (userData['bio'] ?? '') ||
           profileImagePath.value.isNotEmpty;
  }

  String get displayImagePath {
    if (profileImagePath.value.isNotEmpty) {
      return profileImagePath.value;
    }
    return profileImageUrl.value;
  }

  bool get hasProfileImage {
    return profileImagePath.value.isNotEmpty || profileImageUrl.value.isNotEmpty;
  }

  Widget getProfileImageWidget() {
    if (profileImagePath.value.isNotEmpty) {
      return Image.file(
        File(profileImagePath.value),
        fit: BoxFit.cover,
      );
    } else if (profileImageUrl.value.isNotEmpty) {
      return Image.network(
        profileImageUrl.value,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return const Icon(
            Icons.person,
            size: 50,
            color: Colors.grey,
          );
        },
      );
    } else {
      return const Icon(
        Icons.person,
        size: 50,
        color: Colors.grey,
      );
    }
  }
}
