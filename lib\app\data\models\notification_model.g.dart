// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationItem _$NotificationItemFromJson(Map<String, dynamic> json) =>
    NotificationItem(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
      time: DateTime.parse(json['time'] as String),
      isRead: json['isRead'] as bool,
      actionUrl: json['actionUrl'] as String?,
      data: json['data'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$NotificationItemToJson(NotificationItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'time': instance.time.toIso8601String(),
      'isRead': instance.isRead,
      'actionUrl': instance.actionUrl,
      'data': instance.data,
    };

const _$NotificationTypeEnumMap = {
  NotificationType.assignment: 'assignment',
  NotificationType.cafeteria: 'cafeteria',
  NotificationType.library: 'library',
  NotificationType.sports: 'sports',
  NotificationType.grade: 'grade',
  NotificationType.announcement: 'announcement',
};
