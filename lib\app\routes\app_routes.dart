abstract class AppRoutes {
  static const String splash = '/splash';
  static const String intro = '/intro';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String termsOfService = '/terms-of-service';
  static const String privacyPolicy = '/privacy-policy';
  static const String home = '/home';
  static const String mainNavigation = '/main-navigation';
  static const String settings = '/settings';
  static const String settingsNotifications = '/settings/notifications';
  static const String settingsLanguage = '/settings/language';
  static const String settingsAccount = '/settings/account';
  static const String settingsPrivacy = '/settings/privacy';
  static const String profile = '/profile';
  static const String profileEdit = '/profile-edit';
  static const String changePassword = '/change-password';
  static const String wallet = '/wallet';
  static const String notifications = '/notifications';
  static const String about = '/about';

  // Deep linking routes with parameters
  static const String profileWithId = '/profile/:userId';
  static const String notificationWithId = '/notifications/:notificationId';
  static const String walletWithTransaction = '/wallet/:transactionId';

  // Helper methods for parameterized routes
  static String profileById(String userId) => '/profile/$userId';
  static String notificationById(String notificationId) =>
      '/notifications/$notificationId';
  static String walletByTransaction(String transactionId) =>
      '/wallet/$transactionId';

  // Route groups for easier management
  static const List<String> authRoutes = [
    login,
    register,
    forgotPassword,
  ];

  static const List<String> protectedRoutes = [
    mainNavigation,
    home,
    profile,
    profileEdit,
    changePassword,
    wallet,
    notifications,
    settings,
    settingsNotifications,
    settingsLanguage,
    settingsAccount,
    settingsPrivacy,
    about,
  ];

  static const List<String> publicRoutes = [
    splash,
    intro,
    termsOfService,
    privacyPolicy,
  ];

  // Check if a route requires authentication
  static bool isProtectedRoute(String route) {
    return protectedRoutes.contains(route) ||
        route.startsWith('/profile/') ||
        route.startsWith('/notifications/') ||
        route.startsWith('/wallet/');
  }

  // Check if a route is an auth route
  static bool isAuthRoute(String route) {
    return authRoutes.contains(route);
  }

  // Check if a route is public
  static bool isPublicRoute(String route) {
    return publicRoutes.contains(route);
  }
}
