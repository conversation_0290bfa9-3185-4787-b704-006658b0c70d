import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/cards/flat_card.dart';
import '../../core/widgets/flat_list_tile.dart';
import 'about_controller.dart';

class AboutView extends GetView<AboutController> {
  const AboutView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'About',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // App Info Card
            FlatCard(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.school_outlined,
                      size: 40,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Obx(() => Text(
                        controller.appName.value,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      )),
                  const SizedBox(height: 8),
                  Obx(() => Text(
                        'Version ${controller.version.value} (${controller.buildNumber.value})',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      )),
                  const SizedBox(height: 16),
                  Text(
                    'A modern school ERP system with flat design, built with Flutter for seamless student experience.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Developer Info
            _buildSectionHeader(context, 'Developer'),
            const SizedBox(height: 12),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.code,
                  color: Colors.blue,
                  size: 20,
                ),
              ),
              title: 'MidLogic Solutions',
              subtitle: 'Flutter Development Team',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: () => _showDeveloperInfo(context),
            ),

            const SizedBox(height: 24),

            // Support Section
            _buildSectionHeader(context, 'Support'),
            const SizedBox(height: 12),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.help_outline,
                  color: Colors.green,
                  size: 20,
                ),
              ),
              title: 'Contact Support',
              subtitle: 'Get help and support',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.contactSupport,
            ),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.star_outline,
                  color: Colors.orange,
                  size: 20,
                ),
              ),
              title: 'Rate This App',
              subtitle: 'Share your feedback',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.rateApp,
            ),

            const SizedBox(height: 24),

            // Legal Section
            _buildSectionHeader(context, 'Legal'),
            const SizedBox(height: 12),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.privacy_tip_outlined,
                  color: Colors.purple,
                  size: 20,
                ),
              ),
              title: 'Privacy Policy',
              subtitle: 'How we handle your data',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.openPrivacyPolicy,
            ),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.teal.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.description_outlined,
                  color: Colors.teal,
                  size: 20,
                ),
              ),
              title: 'Terms of Service',
              subtitle: 'App usage terms',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.openTermsOfService,
            ),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.indigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.article_outlined,
                  color: Colors.indigo,
                  size: 20,
                ),
              ),
              title: 'Open Source Licenses',
              subtitle: 'Third-party libraries',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.openLicenses,
            ),

            const SizedBox(height: 32),

            // Copyright
            Text(
              '© 2024 MidLogic Solutions. All rights reserved.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
        color: theme.colorScheme.onSurface,
      ),
    );
  }

  void _showDeveloperInfo(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('Developer Info'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Company: MidLogic Solutions'),
            SizedBox(height: 8),
            Text('Technology: Flutter & Dart'),
            SizedBox(height: 8),
            Text('Design: Material Design 3'),
            SizedBox(height: 8),
            Text('Architecture: GetX Pattern'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
