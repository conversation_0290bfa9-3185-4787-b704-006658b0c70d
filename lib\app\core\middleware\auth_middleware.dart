import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/storage_service.dart';
import '../../routes/app_routes.dart';

/// Middleware to check authentication status before accessing protected routes
class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    final storageService = StorageService.to;
    
    // List of routes that require authentication
    final protectedRoutes = [
      AppRoutes.mainNavigation,
      AppRoutes.home,
      AppRoutes.profile,
      AppRoutes.profileEdit,
      AppRoutes.changePassword,
      AppRoutes.wallet,
      AppRoutes.notifications,
      AppRoutes.settings,
      AppRoutes.settingsNotifications,
      AppRoutes.settingsLanguage,
      AppRoutes.settingsAccount,
      AppRoutes.settingsPrivacy,
      AppRoutes.about,
    ];

    // Check if the current route requires authentication
    if (protectedRoutes.contains(route)) {
      // If user is not logged in, redirect to login
      if (!storageService.isLoggedIn) {
        return const RouteSettings(name: AppRoutes.login);
      }
    }

    // List of routes that should redirect to main navigation if user is already logged in
    final authRoutes = [
      AppRoutes.login,
      AppRoutes.register,
      AppRoutes.forgotPassword,
    ];

    // If user is already logged in and trying to access auth routes, redirect to main navigation
    if (authRoutes.contains(route) && storageService.isLoggedIn) {
      return const RouteSettings(name: AppRoutes.mainNavigation);
    }

    return null; // No redirect needed
  }
}

/// Middleware to handle first-time user flow
class OnboardingMiddleware extends GetMiddleware {
  @override
  int? get priority => 2;

  @override
  RouteSettings? redirect(String? route) {
    final storageService = StorageService.to;
    
    // If it's the first time and user is not going to intro, redirect to intro
    if (storageService.isFirstTime && route != AppRoutes.intro && route != AppRoutes.splash) {
      return const RouteSettings(name: AppRoutes.intro);
    }

    return null; // No redirect needed
  }
}

/// Middleware to handle route logging and analytics
class RouteLoggingMiddleware extends GetMiddleware {
  @override
  int? get priority => 3;

  @override
  GetPage? onPageCalled(GetPage? page) {
    if (page != null) {
      // Log route navigation for analytics
      _logRouteNavigation(page.name);
    }
    return page;
  }

  void _logRouteNavigation(String routeName) {
    // In a real app, you would send this to your analytics service
    if (GetPlatform.isDebug) {
      print('🚀 Navigating to: $routeName');
    }
    
    // Example: Send to analytics service
    // AnalyticsService.to.logEvent('route_navigation', {
    //   'route_name': routeName,
    //   'timestamp': DateTime.now().toIso8601String(),
    // });
  }
}

/// Middleware to handle route transitions based on device type
class TransitionMiddleware extends GetMiddleware {
  @override
  int? get priority => 4;

  @override
  GetPage? onPageCalled(GetPage? page) {
    if (page != null) {
      // Customize transitions based on device type or route
      return page.copyWith(
        transition: _getTransitionForRoute(page.name),
        transitionDuration: _getTransitionDurationForRoute(page.name),
      );
    }
    return page;
  }

  Transition _getTransitionForRoute(String routeName) {
    // Use different transitions for different route types
    switch (routeName) {
      case AppRoutes.splash:
      case AppRoutes.mainNavigation:
        return Transition.fadeIn;
      
      case AppRoutes.intro:
        return Transition.rightToLeft;
      
      // Settings sub-screens use slide up transition
      case AppRoutes.settingsNotifications:
      case AppRoutes.settingsLanguage:
      case AppRoutes.settingsAccount:
      case AppRoutes.settingsPrivacy:
        return GetPlatform.isIOS ? Transition.cupertino : Transition.rightToLeft;
      
      // Modal-like screens
      case AppRoutes.profileEdit:
      case AppRoutes.changePassword:
        return GetPlatform.isIOS ? Transition.cupertino : Transition.downToUp;
      
      default:
        return GetPlatform.isIOS ? Transition.cupertino : Transition.rightToLeft;
    }
  }

  Duration _getTransitionDurationForRoute(String routeName) {
    // Faster transitions for frequently accessed routes
    switch (routeName) {
      case AppRoutes.splash:
        return const Duration(milliseconds: 500);
      
      case AppRoutes.mainNavigation:
        return const Duration(milliseconds: 200);
      
      default:
        return const Duration(milliseconds: 300);
    }
  }
}

/// Middleware to handle deep linking and route parameters
class DeepLinkMiddleware extends GetMiddleware {
  @override
  int? get priority => 5;

  @override
  GetPage? onPageCalled(GetPage? page) {
    if (page != null) {
      // Handle deep link parameters
      _handleDeepLinkParameters(page.name);
    }
    return page;
  }

  void _handleDeepLinkParameters(String routeName) {
    final parameters = Get.parameters;
    
    if (parameters.isNotEmpty) {
      if (GetPlatform.isDebug) {
        print('🔗 Deep link parameters for $routeName: $parameters');
      }
      
      // Handle specific deep link scenarios
      switch (routeName) {
        case AppRoutes.profile:
          if (parameters.containsKey('userId')) {
            // Handle viewing another user's profile
            _handleUserProfileDeepLink(parameters['userId']!);
          }
          break;
        
        case AppRoutes.notifications:
          if (parameters.containsKey('notificationId')) {
            // Handle opening specific notification
            _handleNotificationDeepLink(parameters['notificationId']!);
          }
          break;
        
        case AppRoutes.wallet:
          if (parameters.containsKey('transactionId')) {
            // Handle opening specific transaction
            _handleTransactionDeepLink(parameters['transactionId']!);
          }
          break;
      }
    }
  }

  void _handleUserProfileDeepLink(String userId) {
    // Implementation for handling user profile deep link
    if (GetPlatform.isDebug) {
      print('👤 Opening profile for user: $userId');
    }
  }

  void _handleNotificationDeepLink(String notificationId) {
    // Implementation for handling notification deep link
    if (GetPlatform.isDebug) {
      print('🔔 Opening notification: $notificationId');
    }
  }

  void _handleTransactionDeepLink(String transactionId) {
    // Implementation for handling transaction deep link
    if (GetPlatform.isDebug) {
      print('💰 Opening transaction: $transactionId');
    }
  }
}
