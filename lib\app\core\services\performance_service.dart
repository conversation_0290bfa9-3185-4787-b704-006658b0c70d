import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// Performance monitoring and optimization service
class PerformanceService extends GetxService {
  static PerformanceService get to => Get.find();

  // Performance metrics
  final RxMap<String, PerformanceMetric> _metrics = <String, PerformanceMetric>{}.obs;
  final RxDouble _memoryUsage = 0.0.obs;
  final RxInt _frameDrops = 0.obs;
  final RxDouble _averageFrameTime = 0.0.obs;
  
  // Timers and monitoring
  Timer? _memoryMonitorTimer;
  Timer? _frameMonitorTimer;
  final List<Duration> _frameTimes = [];
  
  // Performance thresholds
  static const double memoryWarningThreshold = 100.0; // MB
  static const double frameTimeWarningThreshold = 16.67; // ms (60 FPS)
  static const int maxFrameTimeSamples = 60;

  // Getters
  Map<String, PerformanceMetric> get metrics => _metrics;
  double get memoryUsage => _memoryUsage.value;
  int get frameDrops => _frameDrops.value;
  double get averageFrameTime => _averageFrameTime.value;

  @override
  void onInit() {
    super.onInit();
    _startMonitoring();
  }

  @override
  void onClose() {
    _stopMonitoring();
    super.onClose();
  }

  /// Start performance monitoring
  void _startMonitoring() {
    if (kDebugMode) {
      _startMemoryMonitoring();
      _startFrameMonitoring();
    }
  }

  /// Stop performance monitoring
  void _stopMonitoring() {
    _memoryMonitorTimer?.cancel();
    _frameMonitorTimer?.cancel();
  }

  /// Start memory usage monitoring
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _updateMemoryUsage();
    });
  }

  /// Start frame time monitoring
  void _startFrameMonitoring() {
    _frameMonitorTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateFrameMetrics();
    });
  }

  /// Update memory usage metrics
  Future<void> _updateMemoryUsage() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        // Use platform channel to get memory info
        const platform = MethodChannel('performance/memory');
        final result = await platform.invokeMethod('getMemoryUsage');
        final memoryMB = (result as double?) ?? 0.0;
        
        _memoryUsage.value = memoryMB;
        
        if (memoryMB > memoryWarningThreshold) {
          _logPerformanceWarning('High memory usage: ${memoryMB.toStringAsFixed(1)} MB');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting memory usage: $e');
      }
    }
  }

  /// Update frame time metrics
  void _updateFrameMetrics() {
    if (_frameTimes.isNotEmpty) {
      final avgTime = _frameTimes.fold<double>(0, (sum, time) => sum + time.inMicroseconds) / 
                     _frameTimes.length / 1000; // Convert to milliseconds
      
      _averageFrameTime.value = avgTime;
      
      final droppedFrames = _frameTimes.where((time) => 
        time.inMicroseconds / 1000 > frameTimeWarningThreshold).length;
      
      _frameDrops.value += droppedFrames;
      
      if (avgTime > frameTimeWarningThreshold) {
        _logPerformanceWarning('Frame drops detected: ${avgTime.toStringAsFixed(2)}ms avg');
      }
      
      _frameTimes.clear();
    }
  }

  /// Record frame time
  void recordFrameTime(Duration frameTime) {
    if (kDebugMode) {
      _frameTimes.add(frameTime);
      
      if (_frameTimes.length > maxFrameTimeSamples) {
        _frameTimes.removeAt(0);
      }
    }
  }

  /// Start timing an operation
  PerformanceTimer startTimer(String operationName) {
    return PerformanceTimer(operationName, this);
  }

  /// Record operation completion
  void recordOperation(String operationName, Duration duration) {
    final metric = _metrics[operationName];
    if (metric != null) {
      metric.addSample(duration);
    } else {
      _metrics[operationName] = PerformanceMetric(operationName)..addSample(duration);
    }
    
    if (kDebugMode) {
      print('⏱️ $operationName: ${duration.inMilliseconds}ms');
    }
  }

  /// Get performance summary
  Map<String, dynamic> getPerformanceSummary() {
    return {
      'memory_usage_mb': _memoryUsage.value,
      'frame_drops': _frameDrops.value,
      'average_frame_time_ms': _averageFrameTime.value,
      'operations': _metrics.map((key, value) => MapEntry(key, {
        'count': value.sampleCount,
        'average_ms': value.averageDuration.inMilliseconds,
        'min_ms': value.minDuration.inMilliseconds,
        'max_ms': value.maxDuration.inMilliseconds,
      })),
    };
  }

  /// Clear all metrics
  void clearMetrics() {
    _metrics.clear();
    _frameDrops.value = 0;
    _frameTimes.clear();
  }

  /// Log performance warning
  void _logPerformanceWarning(String message) {
    if (kDebugMode) {
      print('⚠️ Performance Warning: $message');
    }
  }

  /// Optimize memory usage
  Future<void> optimizeMemory() async {
    try {
      // Force garbage collection
      if (Platform.isAndroid || Platform.isIOS) {
        const platform = MethodChannel('performance/memory');
        await platform.invokeMethod('forceGC');
      }
      
      // Clear image cache if memory is high
      if (_memoryUsage.value > memoryWarningThreshold) {
        await _clearImageCache();
      }
      
      if (kDebugMode) {
        print('🧹 Memory optimization completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error optimizing memory: $e');
      }
    }
  }

  /// Clear image cache to free memory
  Future<void> _clearImageCache() async {
    try {
      // Clear Flutter's image cache
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      if (kDebugMode) {
        print('🖼️ Image cache cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing image cache: $e');
      }
    }
  }

  /// Preload critical resources
  Future<void> preloadCriticalResources() async {
    final timer = startTimer('preload_resources');
    
    try {
      // Preload critical images
      await _preloadImages([
        'assets/images/logo.png',
        'assets/images/placeholder.png',
        // Add more critical images
      ]);
      
      // Preload critical data
      await _preloadCriticalData();
      
    } finally {
      timer.stop();
    }
  }

  /// Preload images
  Future<void> _preloadImages(List<String> imagePaths) async {
    for (final path in imagePaths) {
      try {
        await precacheImage(AssetImage(path), Get.context!);
      } catch (e) {
        if (kDebugMode) {
          print('Error preloading image $path: $e');
        }
      }
    }
  }

  /// Preload critical data
  Future<void> _preloadCriticalData() async {
    // Implement critical data preloading
    // This could include user preferences, essential API data, etc.
  }

  /// Monitor widget build performance
  Widget monitorWidget(Widget child, String widgetName) {
    if (!kDebugMode) return child;
    
    return _PerformanceMonitorWidget(
      widgetName: widgetName,
      child: child,
    );
  }
}

/// Performance metric tracking
class PerformanceMetric {
  final String name;
  final List<Duration> _samples = [];
  
  PerformanceMetric(this.name);
  
  void addSample(Duration duration) {
    _samples.add(duration);
    
    // Keep only last 100 samples
    if (_samples.length > 100) {
      _samples.removeAt(0);
    }
  }
  
  int get sampleCount => _samples.length;
  
  Duration get averageDuration {
    if (_samples.isEmpty) return Duration.zero;
    final totalMicroseconds = _samples.fold<int>(0, (sum, duration) => sum + duration.inMicroseconds);
    return Duration(microseconds: totalMicroseconds ~/ _samples.length);
  }
  
  Duration get minDuration => _samples.isEmpty ? Duration.zero : _samples.reduce((a, b) => a < b ? a : b);
  Duration get maxDuration => _samples.isEmpty ? Duration.zero : _samples.reduce((a, b) => a > b ? a : b);
}

/// Performance timer for measuring operations
class PerformanceTimer {
  final String operationName;
  final PerformanceService _service;
  final Stopwatch _stopwatch = Stopwatch();
  
  PerformanceTimer(this.operationName, this._service) {
    _stopwatch.start();
  }
  
  void stop() {
    _stopwatch.stop();
    _service.recordOperation(operationName, _stopwatch.elapsed);
  }
}

/// Widget for monitoring build performance
class _PerformanceMonitorWidget extends StatefulWidget {
  final String widgetName;
  final Widget child;
  
  const _PerformanceMonitorWidget({
    required this.widgetName,
    required this.child,
  });
  
  @override
  State<_PerformanceMonitorWidget> createState() => _PerformanceMonitorWidgetState();
}

class _PerformanceMonitorWidgetState extends State<_PerformanceMonitorWidget> {
  late PerformanceTimer _timer;
  
  @override
  void initState() {
    super.initState();
    _timer = PerformanceService.to.startTimer('widget_build_${widget.widgetName}');
  }
  
  @override
  void dispose() {
    _timer.stop();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
