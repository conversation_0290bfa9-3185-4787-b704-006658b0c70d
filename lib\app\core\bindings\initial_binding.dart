import 'package:get/get.dart';
import '../services/storage_service.dart';
import '../services/network_service.dart';
import '../services/navigation_service.dart';
import '../services/error_handler_service.dart';
import '../services/loading_service.dart';
import '../services/feedback_service.dart';
import '../services/cache_service.dart';
import '../services/performance_service.dart';
import '../services/image_optimization_service.dart';
import '../services/memory_management_service.dart';
import '../services/accessibility_service.dart';
import '../services/media_service.dart';
import '../services/device_service.dart';
import '../services/share_service.dart';
import '../../data/services/auth_service.dart';
import '../../data/services/wallet_service.dart';
import '../../data/services/notification_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize core services synchronously to ensure they're available immediately
    Get.put<StorageService>(StorageService(), permanent: true);
    Get.putAsync<NetworkService>(() => NetworkService().init(),
        permanent: true);

    // Initialize UI and feedback services
    Get.put<NavigationService>(NavigationService(), permanent: true);
    Get.put<ErrorHandlerService>(ErrorHandlerService(), permanent: true);
    Get.put<LoadingService>(LoadingService(), permanent: true);
    Get.put<FeedbackService>(FeedbackService(), permanent: true);

    // Initialize performance and optimization services
    Get.put<CacheService>(CacheService(), permanent: true);
    Get.put<PerformanceService>(PerformanceService(), permanent: true);
    Get.put<ImageOptimizationService>(ImageOptimizationService(),
        permanent: true);
    Get.put<MemoryManagementService>(MemoryManagementService(),
        permanent: true);
    Get.put<AccessibilityService>(AccessibilityService(), permanent: true);

    // Initialize additional core services
    Get.put<DeviceService>(DeviceService(), permanent: true);
    Get.put<MediaService>(MediaService(), permanent: true);
    Get.put<ShareService>(ShareService(), permanent: true);

    // Initialize API services
    Get.put<AuthService>(AuthService(), permanent: true);
    Get.put<WalletService>(WalletService(), permanent: true);
    Get.put<NotificationService>(NotificationService(), permanent: true);
  }
}
