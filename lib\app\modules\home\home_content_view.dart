import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/animated_background.dart';
import '../../core/widgets/cards/flat_card.dart';
import '../../core/widgets/cards/flat_feature_card.dart';
import '../../core/widgets/flat_button.dart';
import 'home_controller.dart';

class HomeContentView extends GetView<HomeController> {
  const HomeContentView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(
          'School Dashboard',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: false,
        actions: [
          IconButton(
            onPressed: () => _showComingSoon(context, 'Notifications'),
            icon: Icon(
              Icons.notifications_outlined,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          IconButton(
            onPressed: controller.logout,
            icon: Icon(
              Icons.logout_rounded,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
      body: AnimatedBackground(
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: 16,
              bottom: 16 + MediaQuery.of(context).viewPadding.bottom,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Welcome Section
                FlatCard(
                  padding: const EdgeInsets.all(24),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Good Morning,',
                              style: theme.textTheme.bodyLarge?.copyWith(
                                color: theme.colorScheme.onSurface
                                    .withValues(alpha: 0.7),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Obx(() => Text(
                                  controller.userName.value,
                                  style:
                                      theme.textTheme.headlineMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                )),
                            const SizedBox(height: 8),
                            Text(
                              'Today is ${_getCurrentDate()}',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurface
                                    .withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color:
                              theme.colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: Icon(
                          Icons.school_outlined,
                          color: theme.colorScheme.primary,
                          size: 30,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Stats Cards
                Text(
                  'Overview',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Attendance',
                        '95%',
                        Icons.check_circle_outline,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Assignments',
                        '8/10',
                        Icons.assignment_outlined,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Grade Average',
                        'A-',
                        Icons.grade_outlined,
                        Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Upcoming Tests',
                        '3',
                        Icons.quiz_outlined,
                        Colors.purple,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Quick Actions
                Text(
                  'Quick Actions',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: FlatButton(
                        text: 'View Timetable',
                        onPressed: () => _showComingSoon(context, 'Timetable'),
                        icon: Icons.schedule_outlined,
                        backgroundColor:
                            theme.colorScheme.primary.withValues(alpha: 0.1),
                        textColor: theme.colorScheme.primary,
                        outlined: true,
                        borderColor:
                            theme.colorScheme.primary.withValues(alpha: 0.3),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: FlatButton(
                        text: 'Assignments',
                        onPressed: () =>
                            _showComingSoon(context, 'Assignments'),
                        icon: Icons.assignment_outlined,
                        backgroundColor:
                            theme.colorScheme.secondary.withValues(alpha: 0.1),
                        textColor: theme.colorScheme.secondary,
                        outlined: true,
                        borderColor:
                            theme.colorScheme.secondary.withValues(alpha: 0.3),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // School Services
                Text(
                  'School Services',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  children: [
                    FlatFeatureCard(
                      icon: Icons.book_outlined,
                      title: 'Library',
                      description: 'Browse books and resources',
                      iconColor: theme.colorScheme.primary,
                      onTap: () => _showComingSoon(context, 'Library'),
                    ),
                    FlatFeatureCard(
                      icon: Icons.restaurant_outlined,
                      title: 'Cafeteria',
                      description: 'View menu and order food',
                      iconColor: Colors.orange,
                      onTap: () => _showComingSoon(context, 'Cafeteria'),
                    ),
                    FlatFeatureCard(
                      icon: Icons.sports_outlined,
                      title: 'Sports',
                      description: 'Sports activities and events',
                      iconColor: Colors.green,
                      onTap: () => _showComingSoon(context, 'Sports'),
                    ),
                    FlatFeatureCard(
                      icon: Icons.event_outlined,
                      title: 'Events',
                      description: 'School events and calendar',
                      iconColor: Colors.purple,
                      onTap: () => _showComingSoon(context, 'Events'),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Recent Activity
                Text(
                  'Recent Activity',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                FlatCard(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildActivityItem(
                        context,
                        Icons.assignment_turned_in,
                        'Math assignment submitted',
                        '2 hours ago',
                        Colors.green,
                      ),
                      const SizedBox(height: 16),
                      _buildActivityItem(
                        context,
                        Icons.quiz,
                        'Science quiz completed',
                        '4 hours ago',
                        Colors.blue,
                      ),
                      const SizedBox(height: 16),
                      _buildActivityItem(
                        context,
                        Icons.announcement,
                        'New announcement posted',
                        '1 day ago',
                        Colors.orange,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    IconData icon,
    String title,
    String time,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              Text(
                time,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return FlatCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 18,
                ),
              ),
              const Spacer(),
              Text(
                value,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  String _getCurrentDate() {
    final now = DateTime.now();
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${months[now.month - 1]} ${now.day}, ${now.year}';
  }

  void _showComingSoon(BuildContext context, String feature) {
    Get.snackbar(
      'Coming Soon',
      '$feature feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }
}
