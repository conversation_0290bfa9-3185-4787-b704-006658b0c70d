import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ShareService extends GetxService {
  static ShareService get to => Get.find();

  Future<void> shareText(String text, {String? subject}) async {
    try {
      await Share.share(
        text,
        subject: subject,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to share text: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> shareFile(
    String filePath, {
    String? text,
    String? subject,
    String? mimeType,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File does not exist');
      }

      await Share.shareXFiles(
        [XFile(filePath, mimeType: mimeType)],
        text: text,
        subject: subject,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to share file: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> shareFiles(
    List<String> filePaths, {
    String? text,
    String? subject,
  }) async {
    try {
      final xFiles = <XFile>[];
      
      for (final path in filePaths) {
        final file = File(path);
        if (await file.exists()) {
          xFiles.add(XFile(path));
        }
      }

      if (xFiles.isEmpty) {
        throw Exception('No valid files to share');
      }

      await Share.shareXFiles(
        xFiles,
        text: text,
        subject: subject,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to share files: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> shareUrl(String url, {String? text, String? subject}) async {
    try {
      final shareText = text != null ? '$text\n$url' : url;
      await Share.share(
        shareText,
        subject: subject,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to share URL: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> openUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        throw Exception('Cannot launch URL: $url');
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to open URL: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> openEmail({
    required String email,
    String? subject,
    String? body,
  }) async {
    try {
      final uri = Uri(
        scheme: 'mailto',
        path: email,
        query: _encodeQueryParameters({
          if (subject != null) 'subject': subject,
          if (body != null) 'body': body,
        }),
      );

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        throw Exception('Cannot open email client');
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to open email: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> openPhone(String phoneNumber) async {
    try {
      final uri = Uri(scheme: 'tel', path: phoneNumber);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        throw Exception('Cannot open phone dialer');
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to open phone dialer: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> openSms(String phoneNumber, {String? message}) async {
    try {
      final uri = Uri(
        scheme: 'sms',
        path: phoneNumber,
        query: message != null ? 'body=$message' : null,
      );

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        throw Exception('Cannot open SMS app');
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to open SMS: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> openWhatsApp(String phoneNumber, {String? message}) async {
    try {
      final url = 'https://wa.me/$phoneNumber${message != null ? '?text=${Uri.encodeComponent(message)}' : ''}';
      await openUrl(url);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to open WhatsApp: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> openMaps({
    double? latitude,
    double? longitude,
    String? address,
  }) async {
    try {
      String url;
      
      if (latitude != null && longitude != null) {
        if (Platform.isIOS) {
          url = 'https://maps.apple.com/?ll=$latitude,$longitude';
        } else {
          url = 'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
        }
      } else if (address != null) {
        final encodedAddress = Uri.encodeComponent(address);
        if (Platform.isIOS) {
          url = 'https://maps.apple.com/?q=$encodedAddress';
        } else {
          url = 'https://www.google.com/maps/search/?api=1&query=$encodedAddress';
        }
      } else {
        throw Exception('Either coordinates or address must be provided');
      }

      await openUrl(url);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to open maps: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> openAppStore({String? appId}) async {
    try {
      String url;
      
      if (Platform.isIOS) {
        url = appId != null 
            ? 'https://apps.apple.com/app/id$appId'
            : 'https://apps.apple.com/';
      } else {
        final packageName = appId ?? 'com.example.nexed_mini'; // Replace with actual package name
        url = 'https://play.google.com/store/apps/details?id=$packageName';
      }

      await openUrl(url);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to open app store: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  Future<void> shareApp({String? customMessage}) async {
    try {
      String message = customMessage ?? 
          'Check out this amazing app! Download it now:';
      
      String url;
      if (Platform.isIOS) {
        url = 'https://apps.apple.com/app/nexed-mini/id123456789'; // Replace with actual App Store URL
      } else {
        url = 'https://play.google.com/store/apps/details?id=com.example.nexed_mini'; // Replace with actual package name
      }

      await shareText('$message\n$url');
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to share app: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    }
  }

  String _encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }
}
