import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import '../models/auth_response.dart';
import '../models/user_model.dart';
import '../../core/services/network_service.dart';

class AuthService extends GetxService {
  static AuthService get to => Get.find();

  final NetworkService _networkService = NetworkService.to;

  Future<AuthResponse> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      final response = await _networkService.post('/auth/login', data: {
        'email': email,
        'password': password,
        'remember_me': rememberMe,
      });

      return AuthResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<AuthResponse> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    try {
      final response = await _networkService.post('/auth/register', data: {
        'name': name,
        'email': email,
        'password': password,
        'password_confirmation': passwordConfirmation,
      });

      return AuthResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> forgotPassword({required String email}) async {
    try {
      await _networkService.post('/auth/forgot-password', data: {
        'email': email,
      });
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> resetPassword({
    required String token,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    try {
      await _networkService.post('/auth/reset-password', data: {
        'token': token,
        'email': email,
        'password': password,
        'password_confirmation': passwordConfirmation,
      });
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<AuthResponse> refreshToken({required String refreshToken}) async {
    try {
      final response = await _networkService.post('/auth/refresh', data: {
        'refresh_token': refreshToken,
      });

      return AuthResponse.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> logout() async {
    try {
      await _networkService.post('/auth/logout');
    } catch (e) {
      // Logout should succeed even if the API call fails
      print('Logout API call failed: $e');
    }
  }

  Future<UserModel> getCurrentUser() async {
    try {
      final response = await _networkService.get('/auth/user');
      return UserModel.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<UserModel> updateProfile({
    String? name,
    String? email,
    String? phone,
    String? bio,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (name != null) data['name'] = name;
      if (email != null) data['email'] = email;
      if (phone != null) data['phone'] = phone;
      if (bio != null) data['bio'] = bio;

      final response = await _networkService.put('/auth/profile', data: data);
      return UserModel.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String passwordConfirmation,
  }) async {
    try {
      await _networkService.put('/auth/change-password', data: {
        'current_password': currentPassword,
        'new_password': newPassword,
        'password_confirmation': passwordConfirmation,
      });
    } catch (e) {
      throw _handleError(e);
    }
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Connection timeout. Please check your internet connection.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'];
          
          if (statusCode == 401) {
            return 'Invalid credentials. Please check your email and password.';
          } else if (statusCode == 422) {
            return message ?? 'Validation error. Please check your input.';
          } else if (statusCode == 429) {
            return 'Too many requests. Please try again later.';
          } else {
            return message ?? 'Server error. Please try again later.';
          }
        case DioExceptionType.cancel:
          return 'Request was cancelled.';
        case DioExceptionType.connectionError:
          return 'No internet connection. Please check your network.';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
    return error.toString();
  }
}
