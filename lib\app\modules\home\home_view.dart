import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/animated_background.dart';
import '../../core/widgets/cards/cards.dart';
import '../../core/theme/app_theme.dart';
import 'home_controller.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBackground(
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Welcome back,',
                            style:
                                Theme.of(context).textTheme.bodyLarge?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withValues(alpha: 0.7),
                                    ),
                          ),
                          Obx(() => Text(
                                controller.userName.value,
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface,
                                    ),
                              )),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: controller.logout,
                      icon: Icon(
                        Icons.logout_rounded,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Welcome Card
                const WelcomeCard(
                  subtitle:
                      'You\'ve successfully logged in to our beautiful glassmorphic app. Enjoy the modern design and smooth animations!',
                ),

                const SizedBox(height: 24),

                // Feature Cards
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  children: [
                    FeatureCard(
                      icon: Icons.palette_rounded,
                      title: 'Beautiful UI',
                      description: 'Glassmorphic design with pastel colors',
                      gradient: Theme.of(context).appColors.primaryGradient,
                    ),
                    FeatureCard(
                      icon: Icons.animation_rounded,
                      title: 'Smooth Animations',
                      description: 'Fluid transitions and micro-interactions',
                      gradient: Theme.of(context).appColors.secondaryGradient,
                    ),
                    FeatureCard(
                      icon: Icons.security_rounded,
                      title: 'Secure',
                      description: 'Advanced security measures',
                      gradient: [
                        Theme.of(context).colorScheme.tertiary,
                        Theme.of(context).colorScheme.primary,
                      ],
                    ),
                    FeatureCard(
                      icon: Icons.speed_rounded,
                      title: 'Fast Performance',
                      description: 'Optimized for speed and efficiency',
                      gradient: [
                        Theme.of(context).colorScheme.secondary,
                        Theme.of(context).colorScheme.tertiary,
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
