import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Modern Material 3 bottom navigation bar with professional animations
/// and flat design principles
class ModernBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<ModernBottomNavigationItem> items;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double height;
  final bool showLabels;
  final EdgeInsets? padding;
  final double borderRadius;
  final bool enableHapticFeedback;

  const ModernBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.height = 72,
    this.showLabels = true,
    this.padding,
    this.borderRadius = 16,
    this.enableHapticFeedback = true,
  });

  @override
  State<ModernBottomNavigation> createState() => _ModernBottomNavigationState();
}

class _ModernBottomNavigationState extends State<ModernBottomNavigation>
    with TickerProviderStateMixin {
  late final AnimationController _indicatorController;
  late final AnimationController _iconController;
  late final Animation<double> _indicatorAnimation;
  late final Animation<double> _iconScaleAnimation;

  @override
  void initState() {
    super.initState();
    _indicatorController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _iconController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _indicatorAnimation = CurvedAnimation(
      parent: _indicatorController,
      curve: Curves.easeInOutCubic,
    );
    _iconScaleAnimation = CurvedAnimation(
      parent: _iconController,
      curve: Curves.elasticOut,
    );
  }

  @override
  void didUpdateWidget(covariant ModernBottomNavigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _indicatorController.forward(from: 0);
      _iconController.forward(from: 0);
      if (widget.enableHapticFeedback) {
        HapticFeedback.lightImpact();
      }
    }
  }

  @override
  void dispose() {
    _indicatorController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final safeBottom = MediaQuery.of(context).padding.bottom;

    final backgroundColor = widget.backgroundColor ?? theme.colorScheme.surface;
    final selectedColor = widget.selectedItemColor ?? theme.colorScheme.primary;
    final unselectedColor = widget.unselectedItemColor ??
        theme.colorScheme.onSurface.withValues(alpha: 0.6);

    return Container(
      height: widget.height + safeBottom,
      padding: widget.padding ?? EdgeInsets.fromLTRB(16, 8, 16, safeBottom),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(widget.borderRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: _ModernNavigationContent(
        height: widget.height,
        items: widget.items,
        currentIndex: widget.currentIndex,
        onTap: widget.onTap,
        selectedColor: selectedColor,
        unselectedColor: unselectedColor,
        showLabels: widget.showLabels,
        indicatorAnimation: _indicatorAnimation,
        iconScaleAnimation: _iconScaleAnimation,
      ),
    );
  }
}

class _ModernNavigationContent extends StatelessWidget {
  final double height;
  final List<ModernBottomNavigationItem> items;
  final int currentIndex;
  final Function(int) onTap;
  final Color selectedColor;
  final Color unselectedColor;
  final bool showLabels;
  final Animation<double> indicatorAnimation;
  final Animation<double> iconScaleAnimation;

  const _ModernNavigationContent({
    required this.height,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    required this.selectedColor,
    required this.unselectedColor,
    required this.showLabels,
    required this.indicatorAnimation,
    required this.iconScaleAnimation,
  });

  @override
  Widget build(BuildContext context) {
    final itemWidth = MediaQuery.of(context).size.width / items.length;

    return SizedBox(
      height: height,
      child: Stack(
        children: [
          // Animated indicator
          AnimatedBuilder(
            animation: indicatorAnimation,
            builder: (context, _) {
              final indicatorOffset =
                  (currentIndex * itemWidth) + (itemWidth / 2) - 24;

              return Positioned(
                left: indicatorOffset,
                top: 8,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOutCubic,
                  width: 48,
                  height: 32,
                  decoration: BoxDecoration(
                    color: selectedColor.withValues(alpha: 0.12),
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              );
            },
          ),
          // Navigation items
          Row(
            children: List.generate(items.length, (index) {
              final isSelected = index == currentIndex;
              return Expanded(
                child: _NavigationItem(
                  item: items[index],
                  isSelected: isSelected,
                  onTap: () => onTap(index),
                  selectedColor: selectedColor,
                  unselectedColor: unselectedColor,
                  showLabel: showLabels,
                  iconScaleAnimation: isSelected ? iconScaleAnimation : null,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}

class _NavigationItem extends StatelessWidget {
  final ModernBottomNavigationItem item;
  final bool isSelected;
  final VoidCallback onTap;
  final Color selectedColor;
  final Color unselectedColor;
  final bool showLabel;
  final Animation<double>? iconScaleAnimation;

  const _NavigationItem({
    required this.item,
    required this.isSelected,
    required this.onTap,
    required this.selectedColor,
    required this.unselectedColor,
    required this.showLabel,
    this.iconScaleAnimation,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          height: 64,
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon with scale animation and badge
              AnimatedBuilder(
                animation:
                    iconScaleAnimation ?? const AlwaysStoppedAnimation(1.0),
                builder: (context, _) {
                  final scale = isSelected && iconScaleAnimation != null
                      ? 1.0 + (iconScaleAnimation!.value * 0.2)
                      : 1.0;

                  return Transform.scale(
                    scale: scale,
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            isSelected
                                ? item.selectedIcon ?? item.icon
                                : item.icon,
                            size: 24,
                            color: isSelected ? selectedColor : unselectedColor,
                          ),
                        ),
                        // Badge for notifications
                        if (item.badgeCount != null && item.badgeCount! > 0)
                          Positioned(
                            right: 4,
                            top: 4,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.error,
                                shape: BoxShape.circle,
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 16,
                                minHeight: 16,
                              ),
                              child: Text(
                                item.badgeCount! > 99
                                    ? '99+'
                                    : '${item.badgeCount}',
                                style: theme.textTheme.labelSmall!.copyWith(
                                  color: theme.colorScheme.onError,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),

              // Label with fade animation
              if (showLabel) ...[
                const SizedBox(height: 4),
                AnimatedDefaultTextStyle(
                  duration: const Duration(milliseconds: 200),
                  style: theme.textTheme.labelSmall!.copyWith(
                    color: isSelected ? selectedColor : unselectedColor,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                  child: Text(
                    item.label,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Navigation item data class for modern bottom navigation
class ModernBottomNavigationItem {
  final IconData icon;
  final IconData? selectedIcon;
  final String label;
  final int? badgeCount;

  const ModernBottomNavigationItem({
    required this.icon,
    required this.label,
    this.selectedIcon,
    this.badgeCount,
  });
}
