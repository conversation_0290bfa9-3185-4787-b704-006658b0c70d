import 'package:get/get.dart';
import '../../core/services/storage_service.dart';
import '../../routes/app_routes.dart';

class HomeController extends GetxController {
  final RxString userName = 'User'.obs;

  @override
  void onInit() {
    super.onInit();
    _loadUserData();
  }

  void _loadUserData() {
    final userData = StorageService.to.userData;
    if (userData != null && userData['name'] != null) {
      userName.value = userData['name'];
    }
  }

  Future<void> logout() async {
    await StorageService.to.clearUserData();
    Get.offAllNamed(AppRoutes.login);
  }
}
