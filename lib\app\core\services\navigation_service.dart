import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../routes/app_routes.dart';
import 'storage_service.dart';

/// Service to handle navigation throughout the app with additional features
class NavigationService extends GetxService {
  static NavigationService get to => Get.find();

  // Navigation history for back button handling
  final RxList<String> _navigationHistory = <String>[].obs;
  List<String> get navigationHistory => _navigationHistory.toList();

  // Current route tracking
  final RxString _currentRoute = ''.obs;
  String get currentRoute => _currentRoute.value;

  @override
  void onInit() {
    super.onInit();
    _setupRouteObserver();
  }

  void _setupRouteObserver() {
    // Listen to route changes
    ever(_currentRoute, (String route) {
      _addToHistory(route);
    });
  }

  void _addToHistory(String route) {
    if (route.isNotEmpty && route != _navigationHistory.lastOrNull) {
      _navigationHistory.add(route);
      
      // Keep history limited to last 10 routes
      if (_navigationHistory.length > 10) {
        _navigationHistory.removeAt(0);
      }
    }
  }

  /// Navigate to a route with optional parameters and result handling
  Future<T?> toNamed<T>(
    String routeName, {
    dynamic arguments,
    Map<String, String>? parameters,
    bool preventDuplicates = true,
  }) async {
    _currentRoute.value = routeName;
    
    return await Get.toNamed<T>(
      routeName,
      arguments: arguments,
      parameters: parameters,
      preventDuplicates: preventDuplicates,
    );
  }

  /// Navigate and replace current route
  Future<T?> offNamed<T>(
    String routeName, {
    dynamic arguments,
    Map<String, String>? parameters,
  }) async {
    _currentRoute.value = routeName;
    
    return await Get.offNamed<T>(
      routeName,
      arguments: arguments,
      parameters: parameters,
    );
  }

  /// Navigate and clear all previous routes
  Future<T?> offAllNamed<T>(
    String routeName, {
    dynamic arguments,
    Map<String, String>? parameters,
  }) async {
    _currentRoute.value = routeName;
    _navigationHistory.clear();
    
    return await Get.offAllNamed<T>(
      routeName,
      arguments: arguments,
      parameters: parameters,
    );
  }

  /// Go back to previous route
  void back<T>({T? result}) {
    if (_navigationHistory.length > 1) {
      _navigationHistory.removeLast();
      _currentRoute.value = _navigationHistory.last;
    }
    Get.back<T>(result: result);
  }

  /// Check if we can go back
  bool canGoBack() {
    return Get.key.currentState?.canPop() ?? false;
  }

  /// Navigate to main navigation with specific tab
  void toMainNavigation({int? tabIndex}) {
    offAllNamed(AppRoutes.mainNavigation, parameters: {
      if (tabIndex != null) 'tab': tabIndex.toString(),
    });
  }

  /// Navigate to profile with optional user ID
  void toProfile({String? userId}) {
    if (userId != null) {
      toNamed(AppRoutes.profileById(userId));
    } else {
      toNamed(AppRoutes.profile);
    }
  }

  /// Navigate to notification with optional notification ID
  void toNotifications({String? notificationId}) {
    if (notificationId != null) {
      toNamed(AppRoutes.notificationById(notificationId));
    } else {
      toNamed(AppRoutes.notifications);
    }
  }

  /// Navigate to wallet with optional transaction ID
  void toWallet({String? transactionId}) {
    if (transactionId != null) {
      toNamed(AppRoutes.walletByTransaction(transactionId));
    } else {
      toNamed(AppRoutes.wallet);
    }
  }

  /// Navigate to settings sub-screens
  void toSettingsNotifications() => toNamed(AppRoutes.settingsNotifications);
  void toSettingsLanguage() => toNamed(AppRoutes.settingsLanguage);
  void toSettingsAccount() => toNamed(AppRoutes.settingsAccount);
  void toSettingsPrivacy() => toNamed(AppRoutes.settingsPrivacy);

  /// Handle authentication flow
  void toLogin({bool clearHistory = false}) {
    if (clearHistory) {
      offAllNamed(AppRoutes.login);
    } else {
      toNamed(AppRoutes.login);
    }
  }

  void toRegister() => toNamed(AppRoutes.register);
  void toForgotPassword() => toNamed(AppRoutes.forgotPassword);

  /// Handle onboarding flow
  void toIntro() => offAllNamed(AppRoutes.intro);

  /// Handle logout
  Future<void> logout() async {
    await StorageService.to.clearUserData();
    offAllNamed(AppRoutes.login);
  }

  /// Show modal bottom sheet with navigation
  Future<T?> showBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = true,
    bool enableDrag = true,
    bool isDismissible = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) async {
    return await Get.bottomSheet<T>(
      child,
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
    );
  }

  /// Show dialog with navigation
  Future<T?> showDialog<T>({
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) async {
    return await Get.dialog<T>(
      child,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel,
    );
  }

  /// Show snackbar with consistent styling
  void showSnackbar({
    required String title,
    required String message,
    SnackPosition position = SnackPosition.BOTTOM,
    Duration duration = const Duration(seconds: 3),
    Color? backgroundColor,
    Color? colorText,
    Widget? icon,
    bool showProgressIndicator = false,
  }) {
    Get.snackbar(
      title,
      message,
      snackPosition: position,
      duration: duration,
      backgroundColor: backgroundColor,
      colorText: colorText,
      icon: icon,
      showProgressIndicator: showProgressIndicator,
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
    );
  }

  /// Show success message
  void showSuccess(String message) {
    showSnackbar(
      title: 'Success',
      message: message,
      backgroundColor: Colors.green.withOpacity(0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.check_circle, color: Colors.white),
    );
  }

  /// Show error message
  void showError(String message) {
    showSnackbar(
      title: 'Error',
      message: message,
      backgroundColor: Colors.red.withOpacity(0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.error, color: Colors.white),
    );
  }

  /// Show info message
  void showInfo(String message) {
    showSnackbar(
      title: 'Info',
      message: message,
      backgroundColor: Colors.blue.withOpacity(0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.info, color: Colors.white),
    );
  }

  /// Show warning message
  void showWarning(String message) {
    showSnackbar(
      title: 'Warning',
      message: message,
      backgroundColor: Colors.orange.withOpacity(0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.warning, color: Colors.white),
    );
  }

  /// Clear navigation history
  void clearHistory() {
    _navigationHistory.clear();
  }

  /// Get previous route
  String? getPreviousRoute() {
    if (_navigationHistory.length > 1) {
      return _navigationHistory[_navigationHistory.length - 2];
    }
    return null;
  }

  /// Check if current route is protected
  bool isCurrentRouteProtected() {
    return AppRoutes.isProtectedRoute(_currentRoute.value);
  }

  /// Check if current route is auth route
  bool isCurrentRouteAuth() {
    return AppRoutes.isAuthRoute(_currentRoute.value);
  }

  /// Handle deep link
  void handleDeepLink(String link) {
    // Parse the deep link and navigate accordingly
    final uri = Uri.parse(link);
    final path = uri.path;
    final queryParams = uri.queryParameters;

    // Convert query parameters to route parameters
    final routeParams = <String, String>{};
    queryParams.forEach((key, value) {
      routeParams[key] = value;
    });

    toNamed(path, parameters: routeParams);
  }
}
