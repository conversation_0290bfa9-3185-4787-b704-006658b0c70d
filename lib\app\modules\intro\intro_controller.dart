import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/services/storage_service.dart';
import '../../routes/app_routes.dart';

class IntroItem {
  final String title;
  final String description;
  final IconData icon;
  final List<Color> gradient;

  IntroItem({
    required this.title,
    required this.description,
    required this.icon,
    required this.gradient,
  });
}

class IntroController extends GetxController {
  final PageController pageController = PageController();
  final RxInt currentPage = 0.obs;
  final RxBool isLastPage = false.obs;

  final List<IntroItem> introItems = [
    IntroItem(
      title: 'Welcome to Nexed Mini',
      description: 'Experience the future of mobile applications with our modern and intuitive design.',
      icon: Icons.rocket_launch_rounded,
      gradient: [const Color(0xFFB8A9FF), const Color(0xFFFFA9E7)],
    ),
    IntroItem(
      title: 'Beautiful Design',
      description: 'Enjoy glassmorphic UI elements with smooth animations and pastel gradients.',
      icon: Icons.palette_rounded,
      gradient: [const Color(0xFFA9FFFF), const Color(0xFFB8A9FF)],
    ),
    IntroItem(
      title: 'Secure & Fast',
      description: 'Your data is protected with advanced security measures and lightning-fast performance.',
      icon: Icons.security_rounded,
      gradient: [const Color(0xFFFFA9E7), const Color(0xFFA9FFFF)],
    ),
  ];

  @override
  void onInit() {
    super.onInit();
    pageController.addListener(() {
      final page = pageController.page?.round() ?? 0;
      currentPage.value = page;
      isLastPage.value = page == introItems.length - 1;
    });
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  void nextPage() {
    if (isLastPage.value) {
      _completeIntro();
    } else {
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void previousPage() {
    if (currentPage.value > 0) {
      pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void skipIntro() {
    _completeIntro();
  }

  void _completeIntro() {
    StorageService.to.isFirstTime = false;
    Get.offAllNamed(AppRoutes.login);
  }
}
