import 'package:get/get.dart';

class MainNavigationController extends GetxController {
  final RxInt currentIndex = 0.obs;
  final RxInt notificationBadgeCount = 3.obs;

  void changeTab(int index) {
    currentIndex.value = index;

    // Clear notification badge when notifications tab is selected
    if (index == 2) {
      // Notifications tab index
      notificationBadgeCount.value = 0;
    }
  }

  void updateNotificationBadge(int count) {
    notificationBadgeCount.value = count;
  }
}
