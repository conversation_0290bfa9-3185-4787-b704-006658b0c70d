import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/widgets/flat_list_tile.dart';
import '../../../shared/widgets/flat_button.dart';
import 'account_management_controller.dart';

class AccountManagementView extends GetView<AccountManagementController> {
  const AccountManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Account Management',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Security Section
            _buildSectionHeader(context, 'Security'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.security,
                      color: Colors.green,
                      size: 20,
                    ),
                  ),
                  title: 'Two-Factor Authentication',
                  subtitle: controller.twoFactorEnabled.value 
                      ? 'Enabled - Extra security layer active'
                      : 'Disabled - Consider enabling for better security',
                  trailing: Switch(
                    value: controller.twoFactorEnabled.value,
                    onChanged: (_) => controller.toggleTwoFactor(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleTwoFactor,
                )),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.lock,
                  color: Colors.orange,
                  size: 20,
                ),
              ),
              title: 'Change Password',
              subtitle: 'Update your account password',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.changePassword,
            ),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.notifications_active,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  title: 'Login Notifications',
                  subtitle: 'Get notified of login attempts',
                  trailing: Switch(
                    value: controller.loginNotifications.value,
                    onChanged: (_) => controller.toggleLoginNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleLoginNotifications,
                )),

            const SizedBox(height: 24),

            // Session Management Section
            _buildSectionHeader(context, 'Session Management'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.timer,
                      color: Colors.purple,
                      size: 20,
                    ),
                  ),
                  title: 'Auto Logout',
                  subtitle: controller.autoLogout.value 
                      ? 'Enabled - ${controller.autoLogoutMinutes.value} minutes'
                      : 'Disabled',
                  trailing: Switch(
                    value: controller.autoLogout.value,
                    onChanged: (_) => controller.toggleAutoLogout(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleAutoLogout,
                )),

            if (controller.autoLogout.value) ...[
              const SizedBox(height: 8),
              Obx(() => FlatListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.teal.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.schedule,
                        color: Colors.teal,
                        size: 20,
                      ),
                    ),
                    title: 'Auto Logout Time',
                    subtitle: '${controller.autoLogoutMinutes.value} minutes',
                    trailing: Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                    onTap: controller.changeAutoLogoutTime,
                  )),
            ],

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.indigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.history,
                  color: Colors.indigo,
                  size: 20,
                ),
              ),
              title: 'Login History',
              subtitle: 'View recent login activity',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.viewLoginHistory,
            ),

            const SizedBox(height: 24),

            // Data Management Section
            _buildSectionHeader(context, 'Data Management'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.cyan.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.sync,
                      color: Colors.cyan,
                      size: 20,
                    ),
                  ),
                  title: 'Data Synchronization',
                  subtitle: 'Sync data across devices',
                  trailing: Switch(
                    value: controller.dataSync.value,
                    onChanged: (_) => controller.toggleDataSync(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleDataSync,
                )),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.download,
                  color: Colors.green,
                  size: 20,
                ),
              ),
              title: 'Export Account Data',
              subtitle: 'Download your account information',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.exportAccountData,
            ),

            const SizedBox(height: 24),

            // Account Status Section
            _buildSectionHeader(context, 'Account Status'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Account Information',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Obx(() => Column(
                        children: [
                          _buildInfoRow(context, 'Last Login', controller.lastLoginDate.value),
                          _buildInfoRow(context, 'Account Created', controller.accountCreatedDate.value),
                          _buildInfoRow(context, 'Failed Login Attempts', '${controller.loginAttempts.value}'),
                          if (controller.accountLocked.value)
                            _buildInfoRow(context, 'Account Status', 'Locked', isWarning: true),
                        ],
                      )),
                ],
              ),
            ),

            if (controller.accountLocked.value) ...[
              const SizedBox(height: 16),
              FlatButton(
                text: 'Unlock Account',
                onPressed: controller.unlockAccount,
                backgroundColor: Colors.orange.withValues(alpha: 0.1),
                textColor: Colors.orange,
                outlined: true,
                borderColor: Colors.orange.withValues(alpha: 0.3),
                width: double.infinity,
              ),
            ],

            const SizedBox(height: 32),

            // Danger Zone
            _buildSectionHeader(context, 'Danger Zone'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.warning,
                        color: Colors.red,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Irreversible Actions',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'These actions cannot be undone. Please proceed with caution.',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 16),
                  FlatButton(
                    text: 'Delete Account',
                    onPressed: controller.deleteAccount,
                    backgroundColor: Colors.red.withValues(alpha: 0.1),
                    textColor: Colors.red,
                    outlined: true,
                    borderColor: Colors.red.withValues(alpha: 0.3),
                    width: double.infinity,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
        color: theme.colorScheme.onSurface,
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value, {bool isWarning = false}) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: isWarning ? Colors.red : theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
