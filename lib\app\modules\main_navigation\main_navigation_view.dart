import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/enhanced_bottom_navigation.dart';
import '../home/<USER>';
import '../wallet/wallet_view.dart';
import '../notifications/notifications_view.dart';
import '../settings/settings_view.dart';
import '../profile/profile_view.dart';
import 'main_navigation_controller.dart';

class MainNavigationView extends GetView<MainNavigationController> {
  const MainNavigationView({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = [
      const HomeContentView(),
      const WalletView(),
      const NotificationsView(),
      const SettingsView(),
      const ProfileView(),
    ];

    return Scaffold(
      body: Obx(() => IndexedStack(
            index: controller.currentIndex.value,
            children: pages,
          )),
      bottomNavigationBar: Obx(() {
        final List<EnhancedBottomNavigationItem> navItems = [
          const EnhancedBottomNavigationItem(
            icon: Icons.home_outlined,
            selectedIcon: Icons.home_rounded,
            label: 'Home',
          ),
          const EnhancedBottomNavigationItem(
            icon: Icons.account_balance_wallet_outlined,
            selectedIcon: Icons.account_balance_wallet_rounded,
            label: 'Wallet',
          ),
          EnhancedBottomNavigationItem(
            icon: Icons.notifications_outlined,
            selectedIcon: Icons.notifications_rounded,
            label: 'Notifications',
            badgeCount: controller.notificationBadgeCount.value > 0
                ? controller.notificationBadgeCount.value
                : null,
          ),
          const EnhancedBottomNavigationItem(
            icon: Icons.settings_outlined,
            selectedIcon: Icons.settings_rounded,
            label: 'Settings',
          ),
          const EnhancedBottomNavigationItem(
            icon: Icons.person_outlined,
            selectedIcon: Icons.person_rounded,
            label: 'Profile',
          ),
        ];

        return EnhancedBottomNavigation(
          currentIndex: controller.currentIndex.value,
          onTap: controller.changeTab,
          items: navItems,
          enableHapticFeedback: true,
          enableRippleEffect: true,
          enableFloatingIndicator: true,
          height: 72,
          borderRadius: 20,
        );
      }),
    );
  }
}
