# Deprecated Flutter Patterns & Modern Alternatives

This document outlines deprecated Flutter patterns to avoid and their modern alternatives for maintaining a clean, future-proof codebase.

## Flutter Version Information
- **Current Flutter SDK**: 3.32.8 (stable channel)
- **Dart Version**: 3.8.1
- **Material Design**: 3.0 (Material 3)

## ❌ Deprecated Patterns to Avoid

### 1. Material Design 2 Components

#### Bottom Navigation Bar (Deprecated)
```dart
// ❌ AVOID: Old Material 2 BottomNavigationBar
BottomNavigationBar(
  items: [
    BottomNavigationBarItem(title: Text('Home'), icon: Icon(Icons.home)),
  ],
)

// ✅ USE: Material 3 NavigationBar
NavigationBar(
  destinations: [
    NavigationDestination(label: 'Home', icon: Icon(Icons.home)),
  ],
)
```

#### Drawer (Deprecated)
```dart
// ❌ AVOID: Old Material 2 Drawer
Drawer(child: ListView(...))

// ✅ USE: Material 3 NavigationDrawer
NavigationDrawer(children: [...])
```

### 2. Theme Data Properties

#### Color Scheme Properties
```dart
// ❌ AVOID: Deprecated color properties
ThemeData(
  accentColor: Colors.blue,
  primaryColorBrightness: Brightness.dark,
  backgroundColor: Colors.white,
  bottomAppBarColor: Colors.grey,
)

// ✅ USE: Material 3 ColorScheme
ThemeData(
  colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
  bottomAppBarTheme: BottomAppBarTheme(color: Colors.grey),
)
```

#### Text Theme Properties
```dart
// ❌ AVOID: Deprecated text theme properties
TextTheme(
  headline1: TextStyle(...),
  headline2: TextStyle(...),
  bodyText1: TextStyle(...),
  bodyText2: TextStyle(...),
)

// ✅ USE: Material 3 text theme properties
TextTheme(
  displayLarge: TextStyle(...),
  displayMedium: TextStyle(...),
  bodyLarge: TextStyle(...),
  bodyMedium: TextStyle(...),
)
```

### 3. Button Widgets

#### Legacy Button Widgets
```dart
// ❌ AVOID: Deprecated button widgets
FlatButton(onPressed: () {}, child: Text('Button'))
RaisedButton(onPressed: () {}, child: Text('Button'))
OutlineButton(onPressed: () {}, child: Text('Button'))

// ✅ USE: Modern Material 3 buttons
TextButton(onPressed: () {}, child: Text('Button'))
ElevatedButton(onPressed: () {}, child: Text('Button'))
OutlinedButton(onPressed: () {}, child: Text('Button'))
```

#### Button Style Properties
```dart
// ❌ AVOID: Deprecated style properties
ElevatedButton.styleFrom(
  primary: Colors.blue,
  onPrimary: Colors.white,
  onSurface: Colors.grey,
)

// ✅ USE: Modern style properties
ElevatedButton.styleFrom(
  backgroundColor: Colors.blue,
  foregroundColor: Colors.white,
  disabledForegroundColor: Colors.grey,
)
```

### 4. Glass Morphism & Skeuomorphic Design

#### Glass Effects (Outdated Design Trend)
```dart
// ❌ AVOID: Glass morphism effects
BackdropFilter(
  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
  child: Container(
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.1),
      border: Border.all(color: Colors.white.withOpacity(0.2)),
    ),
  ),
)

// ✅ USE: Clean flat design with proper Material 3 surfaces
Card(
  elevation: 0,
  color: Theme.of(context).colorScheme.surface,
  child: Container(...),
)
```

### 5. Input Decoration

#### Floating Label Behavior
```dart
// ❌ AVOID: Deprecated hasFloatingPlaceholder
InputDecoration(
  hasFloatingPlaceholder: true,
)

// ✅ USE: floatingLabelBehavior
InputDecoration(
  floatingLabelBehavior: FloatingLabelBehavior.auto,
)
```

### 6. Scrollbar Properties

#### Scrollbar Visibility
```dart
// ❌ AVOID: Deprecated isAlwaysShown
Scrollbar(
  isAlwaysShown: true,
  child: ListView(...),
)

// ✅ USE: thumbVisibility
Scrollbar(
  thumbVisibility: true,
  child: ListView(...),
)
```

## ✅ Modern Best Practices

### 1. Material 3 Design System
- Always use `useMaterial3: true` in ThemeData
- Prefer `ColorScheme.fromSeed()` for consistent color palettes
- Use Material 3 components (NavigationBar, NavigationDrawer, etc.)
- Implement flat design with minimal elevations

### 2. Animation Best Practices
- Use `AnimatedContainer` for simple property animations
- Implement `AnimationController` for complex animations
- Add haptic feedback for user interactions
- Use appropriate curve types (Curves.easeInOutCubic, etc.)

### 3. Accessibility
- Provide semantic labels for all interactive elements
- Ensure proper contrast ratios
- Support screen readers and assistive technologies
- Implement proper focus management

### 4. Performance
- Use `const` constructors wherever possible
- Implement proper widget keys for list items
- Avoid rebuilding expensive widgets unnecessarily
- Use `RepaintBoundary` for complex animations

## 🔧 Migration Tools

### Dart Fix
Many deprecations can be automatically fixed using:
```bash
dart fix --apply
```

### Flutter Upgrade
Keep Flutter updated to the latest stable version:
```bash
flutter upgrade
flutter pub upgrade --major-versions
```

## 📚 Resources

- [Flutter Material 3 Migration Guide](https://docs.flutter.dev/release/breaking-changes/material-3-migration)
- [Material Design 3 Guidelines](https://m3.material.io/)
- [Flutter Breaking Changes](https://docs.flutter.dev/release/breaking-changes)
- [Dart Language Evolution](https://dart.dev/guides/language/evolution)

---

**Last Updated**: 2025-01-08
**Flutter Version**: 3.32.8
**Dart Version**: 3.8.1
