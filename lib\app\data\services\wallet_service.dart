import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import '../models/transaction_model.dart';
import '../../core/services/network_service.dart';

class WalletService extends GetxService {
  static WalletService get to => Get.find();

  final NetworkService _networkService = NetworkService.to;

  Future<double> getBalance() async {
    try {
      final response = await _networkService.get('/wallet/balance');
      return (response.data['balance'] as num).toDouble();
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<List<Transaction>> getTransactions({
    int page = 1,
    int limit = 20,
    String? category,
    TransactionType? type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (category != null) queryParams['category'] = category;
      if (type != null) queryParams['type'] = type.name;
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

      final response = await _networkService.get(
        '/wallet/transactions',
        queryParameters: queryParams,
      );

      final List<dynamic> transactionList = response.data['transactions'];
      return transactionList.map((json) => Transaction.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Transaction> getTransaction(String transactionId) async {
    try {
      final response = await _networkService.get('/wallet/transactions/$transactionId');
      return Transaction.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Transaction> addMoney({
    required double amount,
    required String method,
    String? description,
  }) async {
    try {
      final response = await _networkService.post('/wallet/add-money', data: {
        'amount': amount,
        'method': method,
        'description': description,
      });

      return Transaction.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Transaction> sendMoney({
    required String recipientId,
    required double amount,
    String? description,
  }) async {
    try {
      final response = await _networkService.post('/wallet/send-money', data: {
        'recipient_id': recipientId,
        'amount': amount,
        'description': description,
      });

      return Transaction.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Transaction> makePayment({
    required String merchantId,
    required double amount,
    required String category,
    String? description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _networkService.post('/wallet/payment', data: {
        'merchant_id': merchantId,
        'amount': amount,
        'category': category,
        'description': description,
        'metadata': metadata,
      });

      return Transaction.fromJson(response.data);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<List<String>> getCategories() async {
    try {
      final response = await _networkService.get('/wallet/categories');
      return List<String>.from(response.data['categories']);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<Map<String, double>> getSpendingByCategory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

      final response = await _networkService.get(
        '/wallet/spending-by-category',
        queryParameters: queryParams,
      );

      return Map<String, double>.from(response.data['spending']);
    } catch (e) {
      throw _handleError(e);
    }
  }

  Future<void> setSpendingLimit({
    required String category,
    required double limit,
    required String period, // 'daily', 'weekly', 'monthly'
  }) async {
    try {
      await _networkService.post('/wallet/spending-limit', data: {
        'category': category,
        'limit': limit,
        'period': period,
      });
    } catch (e) {
      throw _handleError(e);
    }
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Connection timeout. Please check your internet connection.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'];
          
          if (statusCode == 400) {
            return message ?? 'Invalid request. Please check your input.';
          } else if (statusCode == 402) {
            return 'Insufficient funds for this transaction.';
          } else if (statusCode == 404) {
            return 'Transaction or resource not found.';
          } else if (statusCode == 422) {
            return message ?? 'Validation error. Please check your input.';
          } else {
            return message ?? 'Server error. Please try again later.';
          }
        case DioExceptionType.cancel:
          return 'Request was cancelled.';
        case DioExceptionType.connectionError:
          return 'No internet connection. Please check your network.';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
    return error.toString();
  }
}
